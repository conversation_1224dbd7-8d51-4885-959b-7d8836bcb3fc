"""Code generation system for creating new agent capabilities."""

import ast
import importlib.util
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import black
import isort
from openai import OpenAI

from ..utils.config import config
from ..utils.decorators import retry
from ..utils.logger import logger


class CodeGenerator:
    """Generates code for new agent capabilities."""
    
    def __init__(self):
        """Initialize the code generator."""
        self.client = OpenAI(
            api_key=config.deepseek_api_key,
            base_url=config.deepseek_base_url
        )
        self.code_store_path = config.code_store_path
        logger.info("Initialized CodeGenerator")
    
    @retry()
    def generate_function_code(
        self,
        function_name: str,
        description: str,
        parameters: List[Dict[str, str]],
        return_type: str = "Any",
        requirements: Optional[List[str]] = None
    ) -> str:
        """Generate Python function code based on description.
        
        Args:
            function_name: Name of the function to generate
            description: Description of what the function should do
            parameters: List of parameter dictionaries with 'name', 'type', 'description'
            return_type: Return type annotation
            requirements: List of required packages/modules
            
        Returns:
            Generated Python code
        """
        # 构建参数字符串
        param_strings = []
        for param in parameters:
            param_str = f"{param['name']}: {param.get('type', 'Any')}"
            if 'default' in param:
                param_str += f" = {param['default']}"
            param_strings.append(param_str)
        
        params_str = ", ".join(param_strings)
        
        # 构建导入语句
        imports = ["from typing import Any, Dict, List, Optional"]
        if requirements:
            for req in requirements:
                if "." in req:
                    imports.append(f"import {req}")
                else:
                    imports.append(f"import {req}")
        
        imports_str = "\n".join(imports)
        
        # 构建参数文档
        param_docs = []
        for param in parameters:
            param_docs.append(f"        {param['name']}: {param.get('description', 'Parameter description')}")
        
        param_docs_str = "\n".join(param_docs) if param_docs else "        None"
        
        prompt = f"""Generate a Python function with the following specifications:

Function name: {function_name}
Description: {description}
Parameters: {params_str}
Return type: {return_type}
Required imports: {imports_str}

Requirements:
1. Include proper type hints
2. Add comprehensive docstring with Args and Returns sections
3. Include error handling with try-catch blocks
4. Add logging statements for important operations
5. Follow Python best practices
6. Make the function robust and production-ready

The function should be complete and ready to use. Include all necessary imports at the top.

Parameter documentation:
{param_docs_str}

Generate only the Python code, no explanations or markdown formatting."""
        
        try:
            response = self.client.chat.completions.create(
                model=config.default_model,
                messages=[
                    {"role": "system", "content": "You are an expert Python developer. Generate clean, well-documented, and robust Python code."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=2000
            )
            
            code = response.choices[0].message.content.strip()
            
            # 清理代码（移除markdown格式）
            if code.startswith("```python"):
                code = code[9:]
            if code.endswith("```"):
                code = code[:-3]
            
            # 格式化代码
            code = self._format_code(code)
            
            logger.info(f"Generated code for function: {function_name}")
            return code
            
        except Exception as e:
            logger.error(f"Failed to generate code for {function_name}: {e}")
            raise
    
    def generate_class_code(
        self,
        class_name: str,
        description: str,
        methods: List[Dict[str, Any]],
        base_classes: Optional[List[str]] = None,
        requirements: Optional[List[str]] = None
    ) -> str:
        """Generate Python class code based on description.
        
        Args:
            class_name: Name of the class to generate
            description: Description of what the class should do
            methods: List of method specifications
            base_classes: List of base class names
            requirements: List of required packages/modules
            
        Returns:
            Generated Python code
        """
        base_classes_str = ""
        if base_classes:
            base_classes_str = f"({', '.join(base_classes)})"
        
        # 构建导入语句
        imports = ["from typing import Any, Dict, List, Optional"]
        if requirements:
            for req in requirements:
                imports.append(f"import {req}")
        
        imports_str = "\n".join(imports)
        
        # 构建方法描述
        methods_desc = []
        for method in methods:
            methods_desc.append(f"- {method['name']}: {method.get('description', 'Method description')}")
        
        methods_desc_str = "\n".join(methods_desc) if methods_desc else "- No specific methods required"
        
        prompt = f"""Generate a Python class with the following specifications:

Class name: {class_name}
Base classes: {base_classes_str}
Description: {description}
Required imports: {imports_str}

Methods to implement:
{methods_desc_str}

Requirements:
1. Include proper type hints for all methods
2. Add comprehensive docstrings for the class and all methods
3. Include __init__ method with appropriate parameters
4. Add error handling where appropriate
5. Include logging statements for important operations
6. Follow Python best practices and design patterns
7. Make the class robust and production-ready

Generate only the Python code, no explanations or markdown formatting."""
        
        try:
            response = self.client.chat.completions.create(
                model=config.default_model,
                messages=[
                    {"role": "system", "content": "You are an expert Python developer. Generate clean, well-documented, and robust Python code following OOP best practices."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=3000
            )
            
            code = response.choices[0].message.content.strip()
            
            # 清理代码
            if code.startswith("```python"):
                code = code[9:]
            if code.endswith("```"):
                code = code[:-3]
            
            # 格式化代码
            code = self._format_code(code)
            
            logger.info(f"Generated code for class: {class_name}")
            return code
            
        except Exception as e:
            logger.error(f"Failed to generate code for class {class_name}: {e}")
            raise
    
    def _format_code(self, code: str) -> str:
        """Format Python code using black and isort.
        
        Args:
            code: Raw Python code
            
        Returns:
            Formatted Python code
        """
        try:
            # 使用isort整理导入
            code = isort.code(code)
            
            # 使用black格式化
            code = black.format_str(code, mode=black.FileMode())
            
            return code
        except Exception as e:
            logger.warning(f"Failed to format code: {e}")
            return code
    
    def validate_code(self, code: str) -> Tuple[bool, Optional[str]]:
        """Validate Python code syntax.
        
        Args:
            code: Python code to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            ast.parse(code)
            return True, None
        except SyntaxError as e:
            error_msg = f"Syntax error at line {e.lineno}: {e.msg}"
            logger.error(f"Code validation failed: {error_msg}")
            return False, error_msg
        except Exception as e:
            error_msg = f"Validation error: {str(e)}"
            logger.error(f"Code validation failed: {error_msg}")
            return False, error_msg
    
    def save_code(self, code: str, filename: str, module_name: str = "generated") -> Path:
        """Save generated code to file.
        
        Args:
            code: Python code to save
            filename: Filename (without extension)
            module_name: Module name for organization
            
        Returns:
            Path to saved file
        """
        module_path = self.code_store_path / module_name
        module_path.mkdir(parents=True, exist_ok=True)
        
        # 创建__init__.py如果不存在
        init_file = module_path / "__init__.py"
        if not init_file.exists():
            init_file.write_text("# Generated module\n")
        
        file_path = module_path / f"{filename}.py"
        file_path.write_text(code, encoding='utf-8')
        
        logger.info(f"Saved generated code to: {file_path}")
        return file_path
    
    def load_and_execute_code(self, file_path: Path) -> Optional[Any]:
        """Load and execute Python code from file.
        
        Args:
            file_path: Path to Python file
            
        Returns:
            Loaded module or None if failed
        """
        if not config.allow_code_execution:
            logger.warning("Code execution is disabled in configuration")
            return None
        
        try:
            spec = importlib.util.spec_from_file_location("dynamic_module", file_path)
            module = importlib.util.module_from_spec(spec)
            
            # 在沙盒模式下限制模块访问
            if config.sandbox_mode:
                # 这里可以添加沙盒限制逻辑
                pass
            
            spec.loader.exec_module(module)
            logger.info(f"Successfully loaded module from: {file_path}")
            return module
            
        except Exception as e:
            logger.error(f"Failed to load module from {file_path}: {e}")
            return None
