#!/usr/bin/env python3
"""
能力执行器 - 负责执行匹配到的能力
"""

import logging
import json
import importlib.util
import inspect
from typing import Dict, List, Any, Optional
from pathlib import Path
from src.self_expanding_agent.extensions.module_integrator import ModuleIntegrator

logger = logging.getLogger(__name__)


class CapabilityExecutor:
    """能力执行器，负责执行匹配到的能力"""
    
    def __init__(self):
        """初始化能力执行器"""
        self.integrator = ModuleIntegrator()
        logger.info("Initialized CapabilityExecutor")
    
    def execute_strategy(self, strategy: Dict[str, Any], analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """执行策略
        
        Args:
            strategy: 执行策略
            analysis: 任务分析结果
            context: 可选上下文
            
        Returns:
            执行结果
        """
        logger.info("Starting strategy execution")
        
        steps = strategy.get("steps", [])
        if not steps:
            return {"error": "No execution steps provided"}
        
        results = []
        final_result = None
        
        for i, step in enumerate(steps):
            try:
                logger.info(f"Executing step {i+1}/{len(steps)}: {step.get('action', 'unknown')}")
                
                step_result = self._execute_step(step, analysis, context)
                results.append({
                    "step_index": i,
                    "step": step,
                    "result": step_result,
                    "status": "success"
                })
                
                # 更新最终结果
                final_result = step_result
                
                logger.info(f"Step {i+1} completed successfully")
                
            except Exception as e:
                logger.error(f"Step {i+1} failed: {e}")
                results.append({
                    "step_index": i,
                    "step": step,
                    "error": str(e),
                    "status": "failed"
                })
                
                # 如果是关键步骤失败，可以选择停止或继续
                if step.get("critical", True):
                    break
        
        return {
            "final_result": final_result,
            "step_results": results,
            "total_steps": len(steps),
            "successful_steps": len([r for r in results if r["status"] == "success"])
        }
    
    def _execute_step(self, step: Dict[str, Any], analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """执行单个步骤
        
        Args:
            step: 执行步骤
            analysis: 任务分析结果
            context: 可选上下文
            
        Returns:
            步骤执行结果
        """
        action = step.get("action", "")
        
        if action == "call_capability":
            return self._call_capability(step, analysis, context)
        elif action == "direct_computation":
            return self._direct_computation(step, analysis, context)
        elif action == "data_processing":
            return self._data_processing(step, analysis, context)
        elif action == "smart_invoke":
            return self._smart_invoke(step, analysis, context)
        else:
            raise ValueError(f"Unknown action type: {action}")
    
    def _call_capability(self, step: Dict[str, Any], analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """调用指定能力
        
        Args:
            step: 执行步骤
            analysis: 任务分析结果
            context: 可选上下文
            
        Returns:
            能力执行结果
        """
        capability_name = step.get("capability_name", "")
        function_name = step.get("function_name", "")
        parameters = step.get("parameters", [])
        
        logger.info(f"Calling capability: {capability_name}.{function_name}")
        
        # 预处理参数
        processed_params = self._process_parameters(parameters, analysis, context)
        
        try:
            # 使用模块集成器调用能力
            result = self.integrator.call_capability_function(
                processed_params,
                capability_name=capability_name.replace(" ", "_").replace("-", "_"),
                function_name=function_name
            )
            
            logger.info(f"Capability call successful: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Capability call failed: {e}")
            
            # 尝试智能推断函数名
            alternative_result = self._try_alternative_function_names(
                capability_name, processed_params, analysis
            )
            
            if alternative_result is not None:
                return alternative_result
            
            raise e
    
    def _smart_invoke(self, step: Dict[str, Any], analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """智能调用 - 自动发现和调用最合适的函数
        
        Args:
            step: 执行步骤
            analysis: 任务分析结果
            context: 可选上下文
            
        Returns:
            执行结果
        """
        capability_name = step.get("capability_name", "")
        code_path = step.get("code_path", "")
        parameters = step.get("parameters", [])
        
        logger.info(f"Smart invoking capability: {capability_name}")
        
        if not code_path:
            raise ValueError(f"No code path provided for capability: {capability_name}")
        
        # 动态加载模块并发现函数
        functions = self._discover_functions(code_path)
        
        if not functions:
            raise ValueError(f"No functions found in capability: {capability_name}")
        
        # 预处理参数
        processed_params = self._process_parameters(parameters, analysis, context)
        
        # 尝试调用每个函数，直到成功
        for func_name, func_info in functions.items():
            try:
                logger.info(f"Trying function: {func_name}")
                
                # 根据函数签名调整参数
                adjusted_params = self._adjust_parameters_for_function(
                    processed_params, func_info["signature"], analysis
                )
                
                result = self.integrator.call_capability_function(
                    adjusted_params,
                    capability_name=capability_name.replace(" ", "_").replace("-", "_"),
                    function_name=func_name
                )
                
                logger.info(f"Smart invoke successful with function: {func_name}")
                return result
                
            except Exception as e:
                logger.debug(f"Function {func_name} failed: {e}")
                continue
        
        raise ValueError(f"All functions failed for capability: {capability_name}")
    
    def _discover_functions(self, code_path: str) -> Dict[str, Dict[str, Any]]:
        """发现代码文件中的函数
        
        Args:
            code_path: 代码文件路径
            
        Returns:
            函数信息字典
        """
        functions = {}
        
        try:
            # 动态加载模块
            spec = importlib.util.spec_from_file_location("capability_module", code_path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                # 发现所有函数
                for name, obj in inspect.getmembers(module, inspect.isfunction):
                    if not name.startswith("_"):  # 忽略私有函数
                        signature = inspect.signature(obj)
                        functions[name] = {
                            "function": obj,
                            "signature": signature,
                            "doc": obj.__doc__ or ""
                        }
                        
        except Exception as e:
            logger.error(f"Failed to discover functions in {code_path}: {e}")
        
        return functions
    
    def _process_parameters(self, parameters: List[Any], analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> List[Any]:
        """预处理参数
        
        Args:
            parameters: 原始参数列表
            analysis: 任务分析结果
            context: 可选上下文
            
        Returns:
            处理后的参数列表
        """
        processed = []
        
        for param in parameters:
            if isinstance(param, str):
                # 尝试从分析结果中提取实际值
                if param.startswith("number"):
                    # 提取数字
                    import re
                    numbers = re.findall(r'\d+(?:\.\d+)?', param)
                    if numbers:
                        processed.append(float(numbers[0]))
                    else:
                        processed.append(param)
                elif param in ["addition", "add", "+"]:
                    processed.append("+")
                elif param in ["subtraction", "subtract", "-"]:
                    processed.append("-")
                elif param in ["multiplication", "multiply", "*"]:
                    processed.append("*")
                elif param in ["division", "divide", "/"]:
                    processed.append("/")
                else:
                    processed.append(param)
            else:
                processed.append(param)
        
        return processed
    
    def _adjust_parameters_for_function(self, parameters: List[Any], signature: inspect.Signature, analysis: Dict[str, Any]) -> List[Any]:
        """根据函数签名调整参数
        
        Args:
            parameters: 参数列表
            signature: 函数签名
            analysis: 任务分析结果
            
        Returns:
            调整后的参数列表
        """
        # 获取函数参数信息
        param_names = list(signature.parameters.keys())
        
        # 如果参数数量匹配，直接返回
        if len(parameters) == len(param_names):
            return parameters
        
        # 尝试从分析结果中补充参数
        if len(parameters) < len(param_names):
            inputs = analysis.get("inputs", [])
            
            # 尝试从inputs中提取数字
            numbers = []
            for inp in inputs:
                if isinstance(inp, str):
                    import re
                    found_numbers = re.findall(r'\d+(?:\.\d+)?', inp)
                    numbers.extend([float(n) for n in found_numbers])
            
            # 补充缺失的参数
            while len(parameters) < len(param_names) and numbers:
                parameters.append(numbers.pop(0))
        
        return parameters[:len(param_names)]  # 截断多余的参数
    
    def _try_alternative_function_names(self, capability_name: str, parameters: List[Any], analysis: Dict[str, Any]) -> Any:
        """尝试替代的函数名
        
        Args:
            capability_name: 能力名称
            parameters: 参数列表
            analysis: 任务分析结果
            
        Returns:
            执行结果或None
        """
        # 常见的函数名模式
        alternative_names = [
            "main",
            "execute",
            "run",
            "process",
            "calculate",
            "compute",
            "perform",
            capability_name.replace(" ", "_").replace("-", "_"),
            capability_name.split()[-1] if " " in capability_name else capability_name
        ]
        
        for func_name in alternative_names:
            try:
                result = self.integrator.call_capability_function(
                    parameters,
                    capability_name=capability_name.replace(" ", "_").replace("-", "_"),
                    function_name=func_name
                )
                logger.info(f"Alternative function name worked: {func_name}")
                return result
            except Exception:
                continue
        
        return None
    
    def _direct_computation(self, step: Dict[str, Any], analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """直接计算
        
        Args:
            step: 执行步骤
            analysis: 任务分析结果
            context: 可选上下文
            
        Returns:
            计算结果
        """
        # 从分析中提取数字和操作
        inputs = analysis.get("inputs", [])
        task_type = analysis.get("task_type", "")
        
        if "arithmetic" in task_type.lower() and len(inputs) >= 2:
            try:
                # 提取数字
                numbers = []
                for inp in inputs:
                    if isinstance(inp, str):
                        import re
                        found_numbers = re.findall(r'\d+(?:\.\d+)?', inp)
                        numbers.extend([float(n) for n in found_numbers])
                
                if len(numbers) >= 2:
                    num1, num2 = numbers[0], numbers[1]
                    
                    # 从子任务推断操作
                    sub_tasks = analysis.get("sub_tasks", [])
                    operator = "+"  # 默认
                    
                    for task in sub_tasks:
                        task_lower = task.lower()
                        if "add" in task_lower or "sum" in task_lower:
                            operator = "+"
                        elif "subtract" in task_lower or "minus" in task_lower:
                            operator = "-"
                        elif "multiply" in task_lower or "times" in task_lower:
                            operator = "*"
                        elif "divide" in task_lower:
                            operator = "/"
                    
                    # 执行计算
                    if operator == "+":
                        result = num1 + num2
                    elif operator == "-":
                        result = num1 - num2
                    elif operator == "*":
                        result = num1 * num2
                    elif operator == "/":
                        result = num1 / num2 if num2 != 0 else "Error: Division by zero"
                    else:
                        result = "Error: Unknown operator"
                    
                    logger.info(f"Direct computation: {num1} {operator} {num2} = {result}")
                    return result
                    
            except Exception as e:
                logger.error(f"Direct computation failed: {e}")
                return f"Computation error: {str(e)}"
        
        return "Direct computation not applicable"
    
    def _data_processing(self, step: Dict[str, Any], analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """数据处理
        
        Args:
            step: 执行步骤
            analysis: 任务分析结果
            context: 可选上下文
            
        Returns:
            处理结果
        """
        # 简单的数据处理实现
        processing_type = step.get("processing_type", "")
        data = step.get("data", analysis.get("inputs", []))
        
        if processing_type == "extract_numbers":
            numbers = []
            for item in data:
                if isinstance(item, str):
                    import re
                    found_numbers = re.findall(r'\d+(?:\.\d+)?', item)
                    numbers.extend([float(n) for n in found_numbers])
            return numbers
        
        return data
