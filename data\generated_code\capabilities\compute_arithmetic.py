import logging
from typing import Any, Dict, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def compute_arithmetic(num1: float, num2: float, operator: str) -> float:
    """
    Performs basic arithmetic operations (addition, subtraction, multiplication, division) on two numbers.

    Args:
        num1: The first operand in the arithmetic operation.
        num2: The second operand in the arithmetic operation.
        operator: The arithmetic operator to be applied. Supported operators are '+', '-', '*', and '/'.

    Returns:
        float: The result of the arithmetic operation.

    Raises:
        ValueError: If the operator is not one of the supported operators.
        ZeroDivisionError: If division by zero is attempted.
    """
    try:
        logger.info(f"Performing operation: {num1} {operator} {num2}")

        if operator == "+":
            result = num1 + num2
        elif operator == "-":
            result = num1 - num2
        elif operator == "*":
            result = num1 * num2
        elif operator == "/":
            if num2 == 0:
                logger.error("Division by zero attempted")
                raise ZeroDivisionError("Division by zero is not allowed")
            result = num1 / num2
        else:
            logger.error(f"Unsupported operator: {operator}")
            raise ValueError(
                f"Unsupported operator: {operator}. Supported operators are '+', '-', '*', '/'"
            )

        logger.info(f"Operation result: {result}")
        return result

    except ZeroDivisionError as e:
        logger.error(f"ZeroDivisionError: {e}")
        raise
    except ValueError as e:
        logger.error(f"ValueError: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise
