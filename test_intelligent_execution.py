#!/usr/bin/env python3
"""测试智能执行系统"""

from src.self_expanding_agent.core.master_agent import MasterAgent
import json

def test_intelligent_execution_flow():
    """测试完整的智能执行流程"""
    print("🚀 测试智能执行流程")
    print("=" * 60)
    
    agent = MasterAgent()
    
    # 测试任务
    test_tasks = [
        "计算 15 + 27 的结果",
        "计算 100 - 45",
        "计算 8 * 9"
    ]
    
    for i, task in enumerate(test_tasks, 1):
        print(f"\n📋 测试 {i}: {task}")
        print("-" * 40)
        
        try:
            # 执行完整流程
            result = agent.process_task(task)
            
            print(f"📊 执行结果:")
            print(f"  - 成功: {result.get('success', False)}")
            
            if result.get('success'):
                execution_result = result.get('result', {})
                method = execution_result.get('method', 'unknown')
                final_result = execution_result.get('result')
                
                print(f"  - 执行方法: {method}")
                print(f"  - 最终结果: {final_result}")
                
                if method == "existing_capability":
                    capability_used = execution_result.get('capability_used', 'unknown')
                    confidence = execution_result.get('confidence', 0)
                    print(f"  - 使用的功能: {capability_used}")
                    print(f"  - 匹配置信度: {confidence}")
                    
                elif method == "self_evolution":
                    new_capability = execution_result.get('new_capability_generated', 'unknown')
                    print(f"  - 新生成的功能: {new_capability}")
                    
                if result.get('new_capabilities_added'):
                    print(f"  - 新增功能: {', '.join(result['new_capabilities_added'])}")
            else:
                print(f"  - 错误: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            import traceback
            traceback.print_exc()
        
        print()


def test_capability_matching():
    """测试能力匹配机制"""
    print("\n🔍 测试能力匹配机制")
    print("=" * 60)
    
    agent = MasterAgent()
    
    # 测试不同类型的任务
    test_cases = [
        {
            "task": "计算两个数的和",
            "expected_match": True,
            "description": "应该匹配到算术计算功能"
        },
        {
            "task": "将文本转换为大写",
            "expected_match": False,
            "description": "应该没有匹配的功能，触发自进化"
        },
        {
            "task": "计算圆的面积",
            "expected_match": False,
            "description": "应该没有匹配的功能，触发自进化"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        task = test_case["task"]
        expected_match = test_case["expected_match"]
        description = test_case["description"]
        
        print(f"\n📋 测试 {i}: {task}")
        print(f"📝 期望: {description}")
        print("-" * 40)
        
        try:
            # 1. 分析任务
            analysis = agent._analyze_task(task)
            print(f"📊 任务分析: {analysis.get('task_type')} - {analysis.get('category')}")
            
            # 2. 智能匹配
            matched_capabilities = agent._intelligent_capability_matching(analysis)
            
            has_match = len(matched_capabilities) > 0
            print(f"🎯 匹配结果: {'有匹配' if has_match else '无匹配'}")
            
            if has_match:
                print(f"📋 匹配的功能:")
                for cap in matched_capabilities:
                    print(f"  - {cap['name']} (置信度: {cap['confidence']:.2f})")
                    print(f"    原因: {cap['reason']}")
            
            # 验证期望
            if has_match == expected_match:
                print("✅ 匹配结果符合期望")
            else:
                print("❌ 匹配结果不符合期望")
                
        except Exception as e:
            print(f"❌ 异常: {e}")


def test_parameter_extraction():
    """测试参数提取功能"""
    print("\n📊 测试参数提取功能")
    print("=" * 60)
    
    agent = MasterAgent()
    
    test_cases = [
        {
            "task": "计算 15 + 27 的结果",
            "expected_numbers": [15, 27],
            "expected_operator": "+"
        },
        {
            "task": "100 减去 45",
            "expected_numbers": [100, 45],
            "expected_operator": "-"
        },
        {
            "task": "8 乘以 9",
            "expected_numbers": [8, 9],
            "expected_operator": "*"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        task = test_case["task"]
        expected_numbers = test_case["expected_numbers"]
        expected_operator = test_case["expected_operator"]
        
        print(f"\n📋 测试 {i}: {task}")
        print("-" * 40)
        
        try:
            # 分析任务
            analysis = agent._analyze_task(task)
            
            # 提取参数
            parameters = agent._extract_parameters_from_analysis(analysis)
            
            print(f"📊 提取的参数: {parameters}")
            
            # 分析参数
            numbers = [p for p in parameters if isinstance(p, (int, float))]
            operators = [p for p in parameters if isinstance(p, str) and p in ['+', '-', '*', '/']]
            
            print(f"🔢 数字: {numbers}")
            print(f"🔣 操作符: {operators}")
            
            # 验证
            numbers_match = set(numbers) == set(expected_numbers)
            operator_match = expected_operator in operators if operators else False
            
            print(f"✅ 数字提取: {'正确' if numbers_match else '错误'}")
            print(f"✅ 操作符提取: {'正确' if operator_match else '错误'}")
            
        except Exception as e:
            print(f"❌ 异常: {e}")


def test_direct_calculation():
    """测试直接计算功能"""
    print("\n🧮 测试直接计算功能")
    print("=" * 60)
    
    agent = MasterAgent()
    
    test_cases = [
        {"numbers": [15, 27], "operator": "+", "expected": 42},
        {"numbers": [100, 45], "operator": "-", "expected": 55},
        {"numbers": [8, 9], "operator": "*", "expected": 72},
        {"numbers": [144, 12], "operator": "/", "expected": 12.0}
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        numbers = test_case["numbers"]
        operator = test_case["operator"]
        expected = test_case["expected"]
        
        print(f"\n📋 测试 {i}: {numbers[0]} {operator} {numbers[1]}")
        print("-" * 40)
        
        try:
            # 构造参数和分析
            parameters = numbers + [operator]
            analysis = {
                "task_description": f"计算 {numbers[0]} {operator} {numbers[1]}",
                "task_type": "arithmetic_calculation",
                "inputs": [f"{numbers[0]} {operator} {numbers[1]}"]
            }
            
            # 执行直接计算
            result = agent._direct_calculation_fallback(parameters, analysis)
            
            print(f"🎯 计算结果: {result}")
            print(f"📊 期望结果: {expected}")
            
            if result == expected:
                print("✅ 计算正确")
            else:
                print("❌ 计算错误")
                
        except Exception as e:
            print(f"❌ 异常: {e}")


if __name__ == "__main__":
    test_intelligent_execution_flow()
    test_capability_matching()
    test_parameter_extraction()
    test_direct_calculation()
