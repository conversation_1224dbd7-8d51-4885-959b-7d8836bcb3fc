#!/usr/bin/env python3
"""测试动态执行系统"""

from src.self_expanding_agent.core.master_agent import MasterAgent
import json

def test_dynamic_execution():
    """测试新的动态执行系统"""
    print("🚀 测试动态执行系统")
    
    agent = MasterAgent()
    
    # 测试算术计算
    task = "计算 15 + 27 的结果"
    print(f"\n📋 测试任务: {task}")
    
    try:
        # 1. 分析任务
        print("1️⃣ 分析任务...")
        analysis = agent._analyze_task(task)
        print(f"📊 分析结果: {json.dumps(analysis, ensure_ascii=False, indent=2)}")
        
        # 2. 生成执行计划
        print("\n2️⃣ 生成执行计划...")
        execution_plan = agent._generate_execution_plan(analysis)
        print(f"📋 执行计划: {json.dumps(execution_plan, ensure_ascii=False, indent=2)}")
        
        # 3. 执行任务
        print("\n3️⃣ 执行任务...")
        result = agent._execute_with_capabilities(execution_plan, analysis)
        print(f"🎯 执行结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 4. 完整流程测试
        print("\n4️⃣ 完整流程测试...")
        full_result = agent.process_task(task)
        print(f"✅ 完整结果: {json.dumps(full_result, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_new_capability():
    """测试新能力的动态调用"""
    print("\n🆕 测试新能力的动态调用")
    
    agent = MasterAgent()
    
    # 测试一个需要新能力的任务
    task = "将文本'Hello World'转换为大写"
    print(f"\n📋 测试任务: {task}")
    
    try:
        result = agent.process_task(task)
        print(f"✅ 结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get('new_capabilities_added'):
            print(f"🆕 新增能力: {', '.join(result['new_capabilities_added'])}")
            
            # 再次执行相同任务，看是否能使用新能力
            print("\n🔄 再次执行相同任务...")
            result2 = agent.process_task(task)
            print(f"✅ 第二次结果: {json.dumps(result2, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_dynamic_execution()
    test_new_capability()
