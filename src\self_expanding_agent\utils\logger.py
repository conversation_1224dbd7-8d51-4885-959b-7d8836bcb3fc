"""Logging configuration for the self-expanding agent system."""

import logging
import sys
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from .config import config


def setup_logger(
    name: str = "self_expanding_agent",
    level: Optional[str] = None,
    log_file: Optional[Path] = None
) -> logging.Logger:
    """Setup logger with rich formatting.
    
    Args:
        name: Logger name
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # 避免重复配置
    if logger.handlers:
        return logger
    
    # 设置日志级别
    log_level = level or config.log_level
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 创建控制台处理器（使用Rich）
    console = Console()
    console_handler = RichHandler(
        console=console,
        show_time=True,
        show_path=True,
        markup=True,
        rich_tracebacks=True
    )
    console_handler.setLevel(getattr(logging, log_level.upper()))
    
    # 设置格式
    formatter = logging.Formatter(
        "%(name)s - %(funcName)s:%(lineno)d - %(message)s"
    )
    console_handler.setFormatter(formatter)
    
    logger.addHandler(console_handler)
    
    # 可选的文件处理器
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, log_level.upper()))
        
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - "
            "%(funcName)s:%(lineno)d - %(message)s"
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger


# 默认logger实例
logger = setup_logger()
