import logging
from typing import Any, Dict, List, Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def convert_temperature(temperature: float, from_unit: str, to_unit: str) -> float:
    """
    Converts a temperature value from one unit to another (Celsius, <PERSON><PERSON><PERSON>heit, or Kelvin).

    Args:
        temperature: The temperature value to be converted.
        from_unit: The original unit of the temperature ('C' for Celsius, 'F' for Fahrenheit, 'K' for Kelvin).
        to_unit: The target unit for conversion ('C' for Celsius, 'F' for Fahrenheit, 'K' for Kelvin).

    Returns:
        float: The converted temperature value in the target unit.

    Raises:
        ValueError: If the input units are invalid or if the temperature is below absolute zero.
    """
    logger.info(f"Converting {temperature} from {from_unit} to {to_unit}")

    # Validate input units
    valid_units = {"C", "F", "K"}
    if from_unit not in valid_units or to_unit not in valid_units:
        error_msg = f"Invalid unit. Valid units are: {valid_units}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # Check for absolute zero violation
    if from_unit == "K" and temperature < 0:
        error_msg = "Temperature cannot be below absolute zero (0K)"
        logger.error(error_msg)
        raise ValueError(error_msg)
    elif from_unit == "C" and temperature < -273.15:
        error_msg = "Temperature cannot be below absolute zero (-273.15°C)"
        logger.error(error_msg)
        raise ValueError(error_msg)
    elif from_unit == "F" and temperature < -459.67:
        error_msg = "Temperature cannot be below absolute zero (-459.67°F)"
        logger.error(error_msg)
        raise ValueError(error_msg)

    try:
        # Convert to Kelvin first if necessary
        if from_unit == to_unit:
            return temperature

        # Convert from_unit to Kelvin
        if from_unit == "C":
            kelvin_temp = temperature + 273.15
        elif from_unit == "F":
            kelvin_temp = (temperature - 32) * 5 / 9 + 273.15
        else:  # from_unit is 'K'
            kelvin_temp = temperature

        # Convert Kelvin to to_unit
        if to_unit == "C":
            converted_temp = kelvin_temp - 273.15
        elif to_unit == "F":
            converted_temp = (kelvin_temp - 273.15) * 9 / 5 + 32
        else:  # to_unit is 'K'
            converted_temp = kelvin_temp

        logger.info(f"Successfully converted temperature: {converted_temp} {to_unit}")
        return converted_temp

    except Exception as e:
        logger.error(f"An error occurred during temperature conversion: {e}")
        raise
