#!/usr/bin/env python3
"""测试热插拔功能"""

from src.self_expanding_agent.core.ability_manager import AbilityManager

def test_hot_plug():
    """测试热插拔功能"""
    print("🔌 测试热插拔功能")
    
    ability_manager = AbilityManager()
    
    # 1. 列出所有能力
    print("\n📊 当前活跃能力:")
    active_abilities = ability_manager.list_abilities()
    for ability in active_abilities:
        print(f"  ✅ {ability.name} ({ability.category})")
    
    print(f"\n总计: {len(active_abilities)} 个活跃能力")
    
    if not active_abilities:
        print("❌ 没有活跃的能力可以测试")
        return
    
    # 2. 选择第一个能力进行测试
    test_ability = active_abilities[0]
    print(f"\n🎯 测试能力: {test_ability.name}")
    
    # 3. 检查当前状态
    current_status = ability_manager.get_ability_status(test_ability.name)
    print(f"📋 当前状态: {'活跃' if current_status else '停用'}")
    
    # 4. 停用能力
    print(f"\n⏸️  停用能力: {test_ability.name}")
    success = ability_manager.deactivate_ability(test_ability.name)
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 5. 检查停用后状态
    new_status = ability_manager.get_ability_status(test_ability.name)
    print(f"📋 停用后状态: {'活跃' if new_status else '停用'}")
    
    # 6. 重新激活能力
    print(f"\n▶️  激活能力: {test_ability.name}")
    success = ability_manager.activate_ability(test_ability.name)
    print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
    
    # 7. 检查激活后状态
    final_status = ability_manager.get_ability_status(test_ability.name)
    print(f"📋 激活后状态: {'活跃' if final_status else '停用'}")
    
    # 8. 测试切换功能
    print(f"\n🔄 测试切换功能")
    success = ability_manager.toggle_ability(test_ability.name)
    print(f"切换结果: {'✅ 成功' if success else '❌ 失败'}")
    
    toggle_status = ability_manager.get_ability_status(test_ability.name)
    print(f"📋 切换后状态: {'活跃' if toggle_status else '停用'}")
    
    # 9. 再次切换回原状态
    ability_manager.toggle_ability(test_ability.name)
    
    # 10. 列出非活跃能力
    print(f"\n📊 非活跃能力:")
    inactive_abilities = ability_manager.list_inactive_abilities()
    for ability in inactive_abilities:
        print(f"  ❌ {ability.name} ({ability.category})")
    
    print(f"\n总计: {len(inactive_abilities)} 个非活跃能力")
    
    print("\n🎉 热插拔功能测试完成！")


if __name__ == "__main__":
    test_hot_plug()
