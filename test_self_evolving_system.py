#!/usr/bin/env python3
"""测试自进化执行系统"""

from src.self_expanding_agent.core.master_agent import MasterAgent
import json

def test_capability_driven_execution():
    """测试能力驱动的执行系统"""
    print("🚀 测试能力驱动的执行系统")
    
    agent = MasterAgent()
    
    # 测试算术计算
    task = "计算 15 + 27 的结果"
    print(f"\n📋 测试任务: {task}")
    
    try:
        # 1. 分析任务
        print("1️⃣ 分析任务...")
        analysis = agent._analyze_task(task)
        print(f"📊 分析结果: {json.dumps(analysis, ensure_ascii=False, indent=2)}")
        
        # 2. 创建能力匹配器并查找能力
        print("\n2️⃣ 匹配能力...")
        matcher = agent._create_capability_matcher()
        matched_capabilities = matcher.find_suitable_capabilities(analysis)
        print(f"🎯 匹配的能力: {json.dumps(matched_capabilities, ensure_ascii=False, indent=2)}")
        
        # 3. 生成执行策略
        print("\n3️⃣ 生成执行策略...")
        strategy = agent._generate_execution_strategy(analysis, matched_capabilities)
        print(f"📋 执行策略: {json.dumps(strategy, ensure_ascii=False, indent=2)}")
        
        # 4. 执行策略
        print("\n4️⃣ 执行策略...")
        executor = agent._create_capability_executor()
        result = executor.execute_strategy(strategy, analysis)
        print(f"🎯 执行结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 5. 完整流程测试
        print("\n5️⃣ 完整流程测试...")
        full_result = agent.process_task(task)
        print(f"✅ 完整结果: {json.dumps(full_result, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_multiple_tasks():
    """测试多种任务类型"""
    print("\n🧪 测试多种任务类型")
    
    agent = MasterAgent()
    
    tasks = [
        "计算 100 - 45",
        "计算 8 * 9", 
        "计算 144 / 12"
    ]
    
    for i, task in enumerate(tasks, 1):
        print(f"\n📋 测试 {i}: {task}")
        
        try:
            result = agent.process_task(task)
            
            if result.get('success'):
                execution_result = result.get('result', {})
                final_result = execution_result.get('result', {}).get('final_result')
                
                print(f"✅ 执行成功")
                print(f"🎯 计算结果: {final_result}")
                
                if result.get('new_capabilities_added'):
                    print(f"🆕 新增能力: {', '.join(result['new_capabilities_added'])}")
            else:
                print(f"❌ 执行失败: {result}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        print("-" * 50)


def test_capability_discovery():
    """测试能力发现机制"""
    print("\n🔍 测试能力发现机制")
    
    agent = MasterAgent()
    
    # 创建一个需要新能力的任务
    task = "将文本转换为大写"
    print(f"\n📋 测试任务: {task}")
    
    try:
        # 分析任务
        analysis = agent._analyze_task(task)
        print(f"📊 任务分析: {analysis.get('task_type')} - {analysis.get('category')}")
        
        # 匹配能力
        matcher = agent._create_capability_matcher()
        matched_capabilities = matcher.find_suitable_capabilities(analysis)
        
        if matched_capabilities:
            print(f"✅ 找到 {len(matched_capabilities)} 个匹配能力:")
            for cap in matched_capabilities:
                print(f"  - {cap['name']} (匹配类型: {cap['match_type']}, 分数: {cap['match_score']})")
        else:
            print("❌ 未找到匹配能力，需要生成新能力")
            
            # 测试完整流程（包括能力生成）
            result = agent.process_task(task)
            print(f"📊 完整结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_capability_driven_execution()
    test_multiple_tasks()
    test_capability_discovery()
