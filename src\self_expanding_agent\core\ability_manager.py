"""Ability management system for agents."""

import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

from pydantic import BaseModel, Field

from ..utils.config import config
from ..utils.logger import logger


class Ability(BaseModel):
    """Ability model."""
    name: str
    description: str
    category: str = "general"
    requirements: List[str] = Field(default_factory=list)
    code_path: Optional[str] = None
    prompt_template: Optional[str] = None
    api_dependencies: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    is_active: bool = True


class AbilityManager:
    """Manages agent abilities and capabilities."""
    
    def __init__(self, db_path: Optional[Union[str, Path]] = None):
        """Initialize the ability manager.

        Args:
            db_path: Path to the SQLite database (string or Path object)
        """
        if db_path:
            self.db_path = Path(db_path)
        else:
            self.db_path = Path(config.database_path)
        self._init_database()
        logger.info(f"Initialized AbilityManager with database: {self.db_path}")
    
    def _init_database(self) -> None:
        """Initialize the SQLite database."""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS abilities (
                    name TEXT PRIMARY KEY,
                    description TEXT NOT NULL,
                    category TEXT DEFAULT 'general',
                    requirements TEXT DEFAULT '[]',
                    code_path TEXT,
                    prompt_template TEXT,
                    api_dependencies TEXT DEFAULT '[]',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS agent_abilities (
                    agent_id TEXT NOT NULL,
                    ability_name TEXT NOT NULL,
                    assigned_at TEXT NOT NULL,
                    PRIMARY KEY (agent_id, ability_name),
                    FOREIGN KEY (ability_name) REFERENCES abilities (name)
                )
            """)
            
            conn.commit()
    
    def register_ability(self, ability: Ability) -> bool:
        """Register a new ability.
        
        Args:
            ability: Ability to register
            
        Returns:
            True if successfully registered
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO abilities 
                    (name, description, category, requirements, code_path, 
                     prompt_template, api_dependencies, created_at, updated_at, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    ability.name,
                    ability.description,
                    ability.category,
                    json.dumps(ability.requirements),
                    ability.code_path,
                    ability.prompt_template,
                    json.dumps(ability.api_dependencies),
                    ability.created_at.isoformat(),
                    ability.updated_at.isoformat(),
                    ability.is_active
                ))
                conn.commit()
            
            logger.info(f"Registered ability: {ability.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to register ability {ability.name}: {e}")
            return False
    
    def get_ability(self, name: str) -> Optional[Ability]:
        """Get an ability by name.
        
        Args:
            name: Ability name
            
        Returns:
            Ability object or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM abilities WHERE name = ? AND is_active = 1",
                    (name,)
                )
                row = cursor.fetchone()
                
                if row:
                    return Ability(
                        name=row['name'],
                        description=row['description'],
                        category=row['category'],
                        requirements=json.loads(row['requirements']),
                        code_path=row['code_path'],
                        prompt_template=row['prompt_template'],
                        api_dependencies=json.loads(row['api_dependencies']),
                        created_at=datetime.fromisoformat(row['created_at']),
                        updated_at=datetime.fromisoformat(row['updated_at']),
                        is_active=bool(row['is_active'])
                    )
        except Exception as e:
            logger.error(f"Failed to get ability {name}: {e}")
        
        return None
    
    def list_abilities(self, category: Optional[str] = None) -> List[Ability]:
        """List all abilities, optionally filtered by category.
        
        Args:
            category: Optional category filter
            
        Returns:
            List of abilities
        """
        abilities = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                if category:
                    cursor = conn.execute(
                        "SELECT * FROM abilities WHERE category = ? AND is_active = 1",
                        (category,)
                    )
                else:
                    cursor = conn.execute(
                        "SELECT * FROM abilities WHERE is_active = 1"
                    )
                
                for row in cursor.fetchall():
                    abilities.append(Ability(
                        name=row['name'],
                        description=row['description'],
                        category=row['category'],
                        requirements=json.loads(row['requirements']),
                        code_path=row['code_path'],
                        prompt_template=row['prompt_template'],
                        api_dependencies=json.loads(row['api_dependencies']),
                        created_at=datetime.fromisoformat(row['created_at']),
                        updated_at=datetime.fromisoformat(row['updated_at']),
                        is_active=bool(row['is_active'])
                    ))
        except Exception as e:
            logger.error(f"Failed to list abilities: {e}")
        
        return abilities
    
    def assign_ability_to_agent(self, agent_id: str, ability_name: str) -> bool:
        """Assign an ability to an agent.
        
        Args:
            agent_id: Agent ID
            ability_name: Ability name
            
        Returns:
            True if successfully assigned
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO agent_abilities 
                    (agent_id, ability_name, assigned_at)
                    VALUES (?, ?, ?)
                """, (agent_id, ability_name, datetime.now().isoformat()))
                conn.commit()
            
            logger.info(f"Assigned ability {ability_name} to agent {agent_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to assign ability {ability_name} to agent {agent_id}: {e}")
            return False
    
    def get_agent_abilities(self, agent_id: str) -> List[Ability]:
        """Get all abilities assigned to an agent.
        
        Args:
            agent_id: Agent ID
            
        Returns:
            List of abilities
        """
        abilities = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT a.* FROM abilities a
                    JOIN agent_abilities aa ON a.name = aa.ability_name
                    WHERE aa.agent_id = ? AND a.is_active = 1
                """, (agent_id,))
                
                for row in cursor.fetchall():
                    abilities.append(Ability(
                        name=row['name'],
                        description=row['description'],
                        category=row['category'],
                        requirements=json.loads(row['requirements']),
                        code_path=row['code_path'],
                        prompt_template=row['prompt_template'],
                        api_dependencies=json.loads(row['api_dependencies']),
                        created_at=datetime.fromisoformat(row['created_at']),
                        updated_at=datetime.fromisoformat(row['updated_at']),
                        is_active=bool(row['is_active'])
                    ))
        except Exception as e:
            logger.error(f"Failed to get abilities for agent {agent_id}: {e}")
        
        return abilities
    
    def search_abilities(self, query: str) -> List[Ability]:
        """Search abilities by name or description.
        
        Args:
            query: Search query
            
        Returns:
            List of matching abilities
        """
        abilities = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM abilities 
                    WHERE (name LIKE ? OR description LIKE ?) AND is_active = 1
                """, (f"%{query}%", f"%{query}%"))
                
                for row in cursor.fetchall():
                    abilities.append(Ability(
                        name=row['name'],
                        description=row['description'],
                        category=row['category'],
                        requirements=json.loads(row['requirements']),
                        code_path=row['code_path'],
                        prompt_template=row['prompt_template'],
                        api_dependencies=json.loads(row['api_dependencies']),
                        created_at=datetime.fromisoformat(row['created_at']),
                        updated_at=datetime.fromisoformat(row['updated_at']),
                        is_active=bool(row['is_active'])
                    ))
        except Exception as e:
            logger.error(f"Failed to search abilities with query '{query}': {e}")
        
        return abilities
    
    def deactivate_ability(self, name: str) -> bool:
        """Deactivate an ability (hot unplug).

        Args:
            name: Ability name

        Returns:
            True if successfully deactivated
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute(
                    "UPDATE abilities SET is_active = 0, updated_at = ? WHERE name = ?",
                    (datetime.now().isoformat(), name)
                )
                conn.commit()

            logger.info(f"Deactivated ability: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to deactivate ability {name}: {e}")
            return False

    def activate_ability(self, name: str) -> bool:
        """Activate an ability (hot plug).

        Args:
            name: Ability name

        Returns:
            True if successfully activated
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Check if ability exists
                cursor = conn.execute(
                    "SELECT name FROM abilities WHERE name = ?",
                    (name,)
                )
                if not cursor.fetchone():
                    logger.error(f"Ability {name} does not exist")
                    return False

                conn.execute(
                    "UPDATE abilities SET is_active = 1, updated_at = ? WHERE name = ?",
                    (datetime.now().isoformat(), name)
                )
                conn.commit()

            logger.info(f"Activated ability: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to activate ability {name}: {e}")
            return False

    def toggle_ability(self, name: str) -> bool:
        """Toggle ability status (activate if inactive, deactivate if active).

        Args:
            name: Ability name

        Returns:
            True if successfully toggled
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT is_active FROM abilities WHERE name = ?",
                    (name,)
                )
                row = cursor.fetchone()

                if not row:
                    logger.error(f"Ability {name} does not exist")
                    return False

                current_status = bool(row[0])
                new_status = not current_status

                conn.execute(
                    "UPDATE abilities SET is_active = ?, updated_at = ? WHERE name = ?",
                    (new_status, datetime.now().isoformat(), name)
                )
                conn.commit()

            action = "Activated" if new_status else "Deactivated"
            logger.info(f"{action} ability: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to toggle ability {name}: {e}")
            return False

    def get_ability_status(self, name: str) -> Optional[bool]:
        """Get the active status of an ability.

        Args:
            name: Ability name

        Returns:
            True if active, False if inactive, None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT is_active FROM abilities WHERE name = ?",
                    (name,)
                )
                row = cursor.fetchone()

                if row:
                    return bool(row[0])
        except Exception as e:
            logger.error(f"Failed to get status for ability {name}: {e}")

        return None

    def list_inactive_abilities(self) -> List[Ability]:
        """List all inactive abilities.

        Returns:
            List of inactive abilities
        """
        abilities = []
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM abilities WHERE is_active = 0"
                )

                for row in cursor.fetchall():
                    abilities.append(Ability(
                        name=row['name'],
                        description=row['description'],
                        category=row['category'],
                        requirements=json.loads(row['requirements']),
                        code_path=row['code_path'],
                        prompt_template=row['prompt_template'],
                        api_dependencies=json.loads(row['api_dependencies']),
                        created_at=datetime.fromisoformat(row['created_at']),
                        updated_at=datetime.fromisoformat(row['updated_at']),
                        is_active=bool(row['is_active'])
                    ))
        except Exception as e:
            logger.error(f"Failed to list inactive abilities: {e}")

        return abilities
