"""Base agent class for the self-expanding agent system."""

import json
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from openai import OpenAI
from pydantic import BaseModel, Field

from ..utils.config import config
from ..utils.decorators import async_retry, retry
from ..utils.logger import logger


class AgentState(BaseModel):
    """Agent state model."""
    agent_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    status: str = "idle"  # idle, thinking, working, error
    capabilities: List[str] = Field(default_factory=list)
    memory: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class BaseAgent(ABC):
    """Base class for all agents in the system."""
    
    def __init__(
        self,
        name: str,
        system_prompt: Optional[str] = None,
        capabilities: Optional[List[str]] = None
    ):
        """Initialize the base agent.
        
        Args:
            name: Agent name
            system_prompt: System prompt for the agent
            capabilities: List of agent capabilities
        """
        self.state = AgentState(
            name=name,
            capabilities=capabilities or []
        )
        self.system_prompt = system_prompt or self._default_system_prompt()
        self.client = self._setup_llm_client()
        self.conversation_history: List[BaseMessage] = []
        
        logger.info(f"Initialized agent: {name} with ID: {self.state.agent_id}")
    
    def _setup_llm_client(self) -> OpenAI:
        """Setup the LLM client (DeepSeek compatible)."""
        return OpenAI(
            api_key=config.deepseek_api_key,
            base_url=config.deepseek_base_url
        )
    
    def _default_system_prompt(self) -> str:
        """Default system prompt for the agent."""
        return f"""You are {self.state.name}, an intelligent agent in a self-expanding agent system.

Your capabilities include: {', '.join(self.state.capabilities) if self.state.capabilities else 'None yet'}

You can:
1. Analyze tasks and break them down into smaller steps
2. Identify when you need new capabilities
3. Request help from other agents or the master agent
4. Learn and adapt based on feedback

Always be helpful, accurate, and honest about your limitations."""
    
    @retry()
    def _call_llm(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """Call the LLM with retry logic.
        
        Args:
            messages: List of messages for the LLM
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            
        Returns:
            LLM response text
        """
        try:
            response = self.client.chat.completions.create(
                model=config.default_model,
                messages=messages,
                temperature=temperature or config.temperature,
                max_tokens=max_tokens or config.max_tokens
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            raise
    
    def add_capability(self, capability: str) -> None:
        """Add a new capability to the agent.
        
        Args:
            capability: Capability description
        """
        if capability not in self.state.capabilities:
            self.state.capabilities.append(capability)
            self.state.updated_at = datetime.now()
            logger.info(f"Added capability '{capability}' to agent {self.state.name}")
    
    def remove_capability(self, capability: str) -> None:
        """Remove a capability from the agent.
        
        Args:
            capability: Capability to remove
        """
        if capability in self.state.capabilities:
            self.state.capabilities.remove(capability)
            self.state.updated_at = datetime.now()
            logger.info(f"Removed capability '{capability}' from agent {self.state.name}")
    
    def has_capability(self, capability: str) -> bool:
        """Check if agent has a specific capability.
        
        Args:
            capability: Capability to check
            
        Returns:
            True if agent has the capability
        """
        return capability in self.state.capabilities
    
    def update_status(self, status: str) -> None:
        """Update agent status.
        
        Args:
            status: New status
        """
        self.state.status = status
        self.state.updated_at = datetime.now()
        logger.debug(f"Agent {self.state.name} status updated to: {status}")
    
    def store_memory(self, key: str, value: Any) -> None:
        """Store information in agent memory.
        
        Args:
            key: Memory key
            value: Value to store
        """
        self.state.memory[key] = value
        self.state.updated_at = datetime.now()
        logger.debug(f"Stored memory: {key} for agent {self.state.name}")
    
    def retrieve_memory(self, key: str, default: Any = None) -> Any:
        """Retrieve information from agent memory.
        
        Args:
            key: Memory key
            default: Default value if key not found
            
        Returns:
            Stored value or default
        """
        return self.state.memory.get(key, default)
    
    def clear_memory(self) -> None:
        """Clear agent memory."""
        self.state.memory.clear()
        self.state.updated_at = datetime.now()
        logger.info(f"Cleared memory for agent {self.state.name}")
    
    @abstractmethod
    def process_task(self, task: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a task. Must be implemented by subclasses.
        
        Args:
            task: Task description
            context: Optional context information
            
        Returns:
            Task result
        """
        pass
    
    def get_state(self) -> Dict[str, Any]:
        """Get current agent state.
        
        Returns:
            Agent state as dictionary
        """
        return self.state.dict()
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"Agent({self.state.name}, {self.state.status}, {len(self.state.capabilities)} capabilities)"
    
    def __repr__(self) -> str:
        """Detailed string representation of the agent."""
        return (
            f"Agent(id={self.state.agent_id}, name={self.state.name}, "
            f"status={self.state.status}, capabilities={self.state.capabilities})"
        )
