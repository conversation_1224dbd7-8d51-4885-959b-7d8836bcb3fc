"""Basic usage example for the self-expanding agent system."""

import asyncio
import os
from pathlib import Path

# 添加src目录到Python路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from self_expanding_agent.core.master_agent import MasterAgent
from self_expanding_agent.workflow.dynamic_workflow import DynamicWorkflow
from self_expanding_agent.extensions.api_discovery import APIDiscovery
from self_expanding_agent.extensions.dependency_manager import DependencyManager
from self_expanding_agent.extensions.module_integrator import ModuleIntegrator
from self_expanding_agent.utils.logger import logger


def setup_environment():
    """设置环境变量和配置."""
    # 设置DeepSeek API密钥（如果没有设置的话）
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("警告: 未设置DEEPSEEK_API_KEY环境变量")
        print("请设置您的DeepSeek API密钥:")
        print("export DEEPSEEK_API_KEY='your-api-key-here'")
        return False
    
    # 创建必要的目录
    Path("data").mkdir(exist_ok=True)
    Path("data/generated_modules").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    return True


def example_1_basic_task_processing():
    """示例1: 基本任务处理."""
    print("\n=== 示例1: 基本任务处理 ===")
    
    # 创建主智能体
    master_agent = MasterAgent()
    
    # 处理一个简单任务
    task = "分析一段文本的情感倾向"
    print(f"任务: {task}")
    
    result = master_agent.process_task(task)
    
    print(f"处理结果: {result['success']}")
    if result['success']:
        print(f"分析结果: {result['analysis']}")
        print(f"能力评估: {result['capability_assessment']}")
        if result['new_capabilities_added']:
            print(f"新增能力: {result['new_capabilities_added']}")
    else:
        print(f"错误: {result['error']}")


def example_2_workflow_generation():
    """示例2: 动态工作流生成."""
    print("\n=== 示例2: 动态工作流生成 ===")
    
    # 创建动态工作流系统
    dynamic_workflow = DynamicWorkflow()
    
    # 定义一个复杂任务
    task_description = "从网页抓取数据，进行数据清洗，然后生成分析报告"
    task_analysis = {
        "task_type": "data_processing",
        "category": "automation",
        "sub_tasks": [
            "网页数据抓取",
            "数据清洗和预处理", 
            "数据分析",
            "报告生成"
        ],
        "required_capabilities": [
            "web_scraping",
            "data_cleaning",
            "data_analysis", 
            "report_generation"
        ],
        "complexity": 7,
        "estimated_time": "30 minutes"
    }
    
    print(f"任务: {task_description}")
    print(f"复杂度: {task_analysis['complexity']}/10")
    
    # 创建并执行工作流
    execution = dynamic_workflow.create_and_execute_workflow(
        task_description=task_description,
        task_analysis=task_analysis,
        initial_state={"target_url": "https://example.com"}
    )
    
    print(f"工作流ID: {execution.workflow_id}")
    print(f"执行状态: {execution.status}")
    if execution.results:
        print(f"执行结果: {execution.results}")


def example_3_api_discovery():
    """示例3: API发现和集成."""
    print("\n=== 示例3: API发现和集成 ===")
    
    # 创建API发现器
    api_discovery = APIDiscovery()
    
    # 发现天气相关的API
    capability = "获取天气信息"
    print(f"寻找能力: {capability}")
    
    discovered_apis = api_discovery.discover_apis_for_capability(capability)
    
    print(f"发现了 {len(discovered_apis)} 个相关API:")
    for api in discovered_apis:
        print(f"- {api.name}: {api.description}")
        print(f"  基础URL: {api.base_url}")
        print(f"  需要API密钥: {api.api_key_required}")
        print(f"  端点数量: {len(api.endpoints)}")
    
    # 测试一个API端点
    if discovered_apis:
        api = discovered_apis[0]
        if api.endpoints:
            endpoint = api.endpoints[0]
            print(f"\n测试API端点: {api.name}.{endpoint.name}")
            
            # 注意：这里需要实际的API密钥才能成功
            test_params = {"q": "Beijing"} if not api.api_key_required else None
            success, response = api_discovery.test_api_endpoint(api, endpoint.name, test_params)
            
            print(f"测试结果: {'成功' if success else '失败'}")
            if response:
                print(f"响应: {str(response)[:200]}...")


def example_4_dependency_management():
    """示例4: 依赖管理."""
    print("\n=== 示例4: 依赖管理 ===")
    
    # 创建依赖管理器
    dep_manager = DependencyManager()
    
    # 分析代码依赖
    sample_code = """
import requests
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt

def analyze_data(data):
    # 数据分析代码
    return data.describe()
"""
    
    print("分析代码依赖...")
    dependencies = dep_manager.analyze_code_dependencies(sample_code)
    print(f"发现依赖: {dependencies}")
    
    # 检查依赖状态
    status = dep_manager.check_dependencies(dependencies)
    print("依赖状态:")
    for pkg, installed in status.items():
        print(f"- {pkg}: {'已安装' if installed else '未安装'}")
    
    # 获取包信息
    for pkg in dependencies[:2]:  # 只检查前两个包
        info = dep_manager.get_package_info(pkg)
        if info:
            print(f"\n{pkg} 信息:")
            print(f"- 版本: {info.version}")
            print(f"- 描述: {info.description}")
            print(f"- 已安装: {info.is_installed}")


def example_5_module_integration():
    """示例5: 模块集成."""
    print("\n=== 示例5: 模块集成 ===")
    
    # 创建模块集成器
    integrator = ModuleIntegrator()
    
    # 生成一个简单的功能模块
    sample_code = '''
"""自动生成的文本处理模块."""

def analyze_sentiment(text: str) -> dict:
    """分析文本情感倾向.
    
    Args:
        text: 要分析的文本
        
    Returns:
        包含情感分析结果的字典
    """
    # 简单的情感分析实现
    positive_words = ["好", "棒", "优秀", "喜欢", "满意"]
    negative_words = ["坏", "差", "糟糕", "讨厌", "不满"]
    
    positive_count = sum(1 for word in positive_words if word in text)
    negative_count = sum(1 for word in negative_words if word in text)
    
    if positive_count > negative_count:
        sentiment = "positive"
        score = positive_count / (positive_count + negative_count + 1)
    elif negative_count > positive_count:
        sentiment = "negative"
        score = negative_count / (positive_count + negative_count + 1)
    else:
        sentiment = "neutral"
        score = 0.5
    
    return {
        "sentiment": sentiment,
        "score": score,
        "positive_words": positive_count,
        "negative_words": negative_count
    }

class TextProcessor:
    """文本处理器类."""
    
    def __init__(self):
        self.processed_count = 0
    
    def process_text(self, text: str) -> str:
        """处理文本."""
        self.processed_count += 1
        return text.strip().lower()
'''
    
    print("集成生成的代码模块...")
    success = integrator.integrate_generated_code(
        code=sample_code,
        module_name="text_analyzer",
        capability_name="sentiment_analysis",
        description="文本情感分析功能",
        auto_install_deps=False  # 这个示例不需要外部依赖
    )
    
    print(f"集成结果: {'成功' if success else '失败'}")
    
    if success:
        # 测试集成的功能
        try:
            result = integrator.call_capability_function(
                "这个产品真的很好用，我很满意！",
                capability_name="sentiment_analysis",
                function_name="analyze_sentiment"
            )
            print(f"情感分析结果: {result}")
        except Exception as e:
            print(f"调用功能时出错: {e}")
    
    # 列出所有集成的能力
    capabilities = integrator.list_integrated_capabilities()
    print(f"\n已集成的能力数量: {len(capabilities)}")
    for cap in capabilities:
        print(f"- {cap['name']}: {cap['description']}")


def example_6_complete_workflow():
    """示例6: 完整的自扩展工作流."""
    print("\n=== 示例6: 完整的自扩展工作流 ===")
    
    # 创建主智能体
    master_agent = MasterAgent()
    
    # 定义一个需要新能力的复杂任务
    complex_task = "创建一个能够从JSON数据中提取特定字段并生成CSV报告的功能"
    
    print(f"复杂任务: {complex_task}")
    print("这个任务可能需要系统自动生成新的代码能力...")
    
    # 处理任务
    result = master_agent.process_task(complex_task)
    
    print(f"\n任务处理完成:")
    print(f"- 成功: {result['success']}")
    print(f"- 任务分析: {result.get('analysis', {}).get('task_type', 'unknown')}")
    print(f"- 复杂度: {result.get('analysis', {}).get('complexity', 'unknown')}")
    print(f"- 缺失能力: {result.get('capability_assessment', {}).get('missing_capabilities', [])}")
    print(f"- 新增能力: {result.get('new_capabilities_added', [])}")
    
    if result['success']:
        print(f"- 执行结果: {result.get('result', {}).get('status', 'unknown')}")
    
    # 显示系统状态
    system_status = master_agent.get_system_status()
    print(f"\n系统状态:")
    print(f"- 主智能体状态: {system_status['master_agent_status']}")
    print(f"- 总能力数: {system_status['total_abilities']}")
    print(f"- 活跃智能体: {system_status['active_agents']}")
    print(f"- 系统健康: {system_status['system_health']}")


def main():
    """主函数."""
    print("自扩展智能体系统 - 使用示例")
    print("=" * 50)
    
    # 设置环境
    if not setup_environment():
        print("环境设置失败，退出...")
        return
    
    try:
        # 运行所有示例
        example_1_basic_task_processing()
        example_2_workflow_generation()
        example_3_api_discovery()
        example_4_dependency_management()
        example_5_module_integration()
        example_6_complete_workflow()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        
    except Exception as e:
        logger.error(f"示例运行出错: {e}")
        print(f"运行出错: {e}")


if __name__ == "__main__":
    main()
