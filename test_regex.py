#!/usr/bin/env python3
"""测试正则表达式"""

import re

def test_regex():
    """测试算术表达式正则匹配"""
    print("🔍 测试正则表达式")
    
    # 测试不同的任务描述
    test_cases = [
        "计算 15 + 27 的结果",
        "15 + 27",
        "计算15+27",
        "100 - 45",
        "8 * 9",
        "144 / 12"
    ]
    
    arithmetic_pattern = r'(\d+(?:\.\d+)?)\s*([+\-*/])\s*(\d+(?:\.\d+)?)'
    
    for task in test_cases:
        print(f"\n📝 测试: '{task}'")
        match = re.search(arithmetic_pattern, task)
        
        if match:
            num1 = float(match.group(1))
            operator = match.group(2)
            num2 = float(match.group(3))
            print(f"✅ 匹配成功: {num1} {operator} {num2}")
            
            # 计算结果
            if operator == "+":
                result = num1 + num2
            elif operator == "-":
                result = num1 - num2
            elif operator == "*":
                result = num1 * num2
            elif operator == "/":
                result = num1 / num2 if num2 != 0 else "Error: Division by zero"
            else:
                result = "Error: Unsupported operator"
            
            print(f"🎯 计算结果: {result}")
        else:
            print("❌ 匹配失败")


def test_analysis_data():
    """测试从分析数据中提取信息"""
    print("\n🔍 测试从分析数据中提取信息")
    
    # 模拟分析结果
    analysis = {
        'task_type': 'arithmetic calculation', 
        'category': 'mathematics', 
        'required_capabilities': ['basic arithmetic operations'], 
        'sub_tasks': ['add the two numbers'], 
        'inputs': ['15', '27'], 
        'outputs': ['42'], 
        'complexity': 1, 
        'estimated_time': 'instantaneous', 
        'dependencies': [], 
        'success_criteria': ['correct sum is calculated']
    }
    
    print(f"📊 分析结果: {analysis}")
    
    # 从inputs中提取数字
    inputs = analysis.get('inputs', [])
    if len(inputs) >= 2:
        try:
            num1 = float(inputs[0])
            num2 = float(inputs[1])
            
            # 从sub_tasks中推断操作
            sub_tasks = analysis.get('sub_tasks', [])
            operator = "+"  # 默认加法
            
            for task in sub_tasks:
                if "add" in task.lower():
                    operator = "+"
                elif "subtract" in task.lower():
                    operator = "-"
                elif "multiply" in task.lower():
                    operator = "*"
                elif "divide" in task.lower():
                    operator = "/"
            
            print(f"✅ 从分析中提取: {num1} {operator} {num2}")
            
            # 计算
            if operator == "+":
                result = num1 + num2
            elif operator == "-":
                result = num1 - num2
            elif operator == "*":
                result = num1 * num2
            elif operator == "/":
                result = num1 / num2 if num2 != 0 else "Error: Division by zero"
            
            print(f"🎯 计算结果: {result}")
            
        except ValueError as e:
            print(f"❌ 数字转换失败: {e}")
    else:
        print("❌ 输入数据不足")


if __name__ == "__main__":
    test_regex()
    test_analysis_data()
