#!/usr/bin/env python3
"""启动脚本 - 自扩展智能体系统"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from self_expanding_agent.core.master_agent import MasterAgent
from self_expanding_agent.utils.logger import logger


def check_environment():
    """检查环境配置."""
    print("🔍 检查环境配置...")
    
    # 检查API密钥
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("❌ 错误: 未设置DEEPSEEK_API_KEY环境变量")
        print("请设置您的DeepSeek API密钥:")
        print("export DEEPSEEK_API_KEY='your-api-key-here'")
        return False
    
    # 创建必要目录
    directories = ["data", "data/generated_modules", "logs"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 环境配置检查完成")
    return True


def interactive_mode():
    """交互模式."""
    print("\n🤖 自扩展智能体系统 - 交互模式")
    print("=" * 50)
    print("输入任务描述，系统将自动分析并执行")
    print("输入 'quit' 或 'exit' 退出")
    print("输入 'status' 查看系统状态")
    print("输入 'help' 查看帮助")
    print("=" * 50)
    
    # 初始化主智能体
    try:
        master_agent = MasterAgent()
        print("✅ 主智能体初始化成功")
    except Exception as e:
        print(f"❌ 主智能体初始化失败: {e}")
        return
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n🎯 请输入任务: ").strip()
            
            if not user_input:
                continue
            
            # 处理特殊命令
            if user_input.lower() in ['quit', 'exit']:
                print("👋 再见！")
                break
            
            elif user_input.lower() == 'status':
                show_system_status(master_agent)
                continue
            
            elif user_input.lower() == 'help':
                show_help()
                continue
            
            # 处理任务
            print(f"\n🔄 处理任务: {user_input}")
            print("-" * 30)
            
            result = master_agent.process_task(user_input)
            
            # 显示结果
            print(f"\n📊 任务处理结果:")
            print(f"✅ 成功: {result['success']}")
            
            if result['success']:
                analysis = result.get('analysis', {})
                print(f"📝 任务类型: {analysis.get('task_type', '未知')}")
                print(f"📈 复杂度: {analysis.get('complexity', '未知')}/10")
                
                capability_assessment = result.get('capability_assessment', {})
                missing_caps = capability_assessment.get('missing_capabilities', [])
                if missing_caps:
                    print(f"🆕 新增能力: {', '.join(missing_caps)}")
                
                execution_result = result.get('result', {})
                if execution_result:
                    print(f"🎯 执行状态: {execution_result.get('status', '未知')}")
            else:
                print(f"❌ 错误: {result.get('error', '未知错误')}")
        
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出...")
            break
        except Exception as e:
            print(f"❌ 处理过程中出错: {e}")
            logger.error(f"Interactive mode error: {e}")


def show_system_status(master_agent):
    """显示系统状态."""
    try:
        status = master_agent.get_system_status()
        
        print("\n📊 系统状态:")
        print(f"🤖 主智能体状态: {status['master_agent_status']}")
        print(f"🧠 总能力数量: {status['total_abilities']}")
        print(f"🔄 活跃智能体: {status['active_agents']}")
        print(f"💚 系统健康: {status['system_health']}")
        
        # 显示能力分类
        capabilities = status.get('capabilities_by_category', {})
        if capabilities:
            print("\n📋 能力分类:")
            for category, count in capabilities.items():
                print(f"  - {category}: {count}")
    
    except Exception as e:
        print(f"❌ 获取系统状态失败: {e}")


def show_help():
    """显示帮助信息."""
    print("\n📖 帮助信息:")
    print("=" * 30)
    print("🎯 任务示例:")
    print("  - 分析一段文本的情感倾向")
    print("  - 创建一个天气查询功能")
    print("  - 从网页抓取数据并生成报告")
    print("  - 实现一个简单的计算器")
    print("  - 生成一个数据可视化图表")
    print()
    print("🔧 特殊命令:")
    print("  - status: 查看系统状态")
    print("  - help: 显示此帮助")
    print("  - quit/exit: 退出程序")
    print()
    print("💡 提示:")
    print("  - 系统会自动分析任务并生成所需能力")
    print("  - 如果缺少能力，系统会自动生成代码")
    print("  - 支持复杂的多步骤工作流")


def demo_mode():
    """演示模式."""
    print("\n🎬 自扩展智能体系统 - 演示模式")
    print("=" * 50)
    
    # 初始化主智能体
    try:
        master_agent = MasterAgent()
        print("✅ 主智能体初始化成功")
    except Exception as e:
        print(f"❌ 主智能体初始化失败: {e}")
        return
    
    # 演示任务列表
    demo_tasks = [
        "分析文本情感倾向",
        "创建一个简单的数学计算功能",
        "实现数据格式转换功能",
        "生成一个基础的数据统计报告"
    ]
    
    print(f"\n🎯 将演示 {len(demo_tasks)} 个任务:")
    for i, task in enumerate(demo_tasks, 1):
        print(f"  {i}. {task}")
    
    input("\n按回车键开始演示...")
    
    for i, task in enumerate(demo_tasks, 1):
        print(f"\n{'='*20} 演示 {i}/{len(demo_tasks)} {'='*20}")
        print(f"🎯 任务: {task}")
        
        try:
            result = master_agent.process_task(task)
            
            print(f"✅ 处理结果: {'成功' if result['success'] else '失败'}")
            
            if result['success']:
                analysis = result.get('analysis', {})
                print(f"📝 任务类型: {analysis.get('task_type', '未知')}")
                print(f"📈 复杂度: {analysis.get('complexity', '未知')}/10")
                
                missing_caps = result.get('capability_assessment', {}).get('missing_capabilities', [])
                if missing_caps:
                    print(f"🆕 新增能力: {', '.join(missing_caps)}")
            else:
                print(f"❌ 错误: {result.get('error', '未知错误')}")
        
        except Exception as e:
            print(f"❌ 演示任务失败: {e}")
        
        if i < len(demo_tasks):
            input("\n按回车键继续下一个演示...")
    
    print(f"\n🎉 演示完成！")
    
    # 显示最终系统状态
    show_system_status(master_agent)


def main():
    """主函数."""
    print("🚀 自扩展智能体系统")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == 'demo':
            demo_mode()
        elif mode == 'interactive':
            interactive_mode()
        elif mode == 'help':
            print("\n📖 使用方法:")
            print("python run_agent.py [mode]")
            print("\n🔧 可用模式:")
            print("  interactive  - 交互模式（默认）")
            print("  demo        - 演示模式")
            print("  help        - 显示帮助")
        else:
            print(f"❌ 未知模式: {mode}")
            print("使用 'python run_agent.py help' 查看帮助")
    else:
        # 默认交互模式
        interactive_mode()


if __name__ == "__main__":
    main()
