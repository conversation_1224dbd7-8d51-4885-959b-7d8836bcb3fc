import logging
import math
import random
from typing import Optional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def calculate_pi(
    method: str = "leibniz", iterations: int = 1000, precision: Optional[int] = None
) -> float:
    """
    Calculate the value of π (pi) using a specified algorithm.

    Args:
        method: The algorithm used to calculate pi. Options: 'leibniz', 'monte_carlo', 'bbp'. Default is 'leibniz'.
        iterations: The number of iterations or terms to use in the calculation. Higher values improve precision but increase computation time. Default is 1000.
        precision: The number of decimal places to round the result. If None, returns the full precision. Default is None.

    Returns:
        float: The calculated value of π, optionally rounded to the specified precision.

    Raises:
        ValueError: If an invalid method is provided or if iterations is not a positive integer.
    """
    try:
        if iterations <= 0:
            raise ValueError("Iterations must be a positive integer.")

        pi_value = 0.0

        if method == "leibniz":
            logger.info("Calculating π using the <PERSON>ibniz formula...")
            pi_value = 0.0
            for k in range(iterations):
                term = (-1) ** k / (2 * k + 1)
                pi_value += term
            pi_value *= 4

        elif method == "monte_carlo":
            logger.info("Calculating π using Monte Carlo simulation...")
            inside_circle = 0
            for _ in range(iterations):
                x = random.random()
                y = random.random()
                if x**2 + y**2 <= 1.0:
                    inside_circle += 1
            pi_value = 4 * inside_circle / iterations

        elif method == "bbp":
            logger.info("Calculating π using the BBP formula...")
            pi_value = 0.0
            for k in range(iterations):
                term = (1 / 16**k) * (
                    4 / (8 * k + 1)
                    - 2 / (8 * k + 4)
                    - 1 / (8 * k + 5)
                    - 1 / (8 * k + 6)
                )
                pi_value += term

        else:
            raise ValueError(
                f"Invalid method: {method}. Choose from 'leibniz', 'monte_carlo', 'bbp'."
            )

        logger.info(f"π calculated successfully using method: {method}.")

        if precision is not None:
            pi_value = round(pi_value, precision)

        return pi_value

    except ValueError as ve:
        logger.error(f"ValueError in calculate_pi: {ve}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in calculate_pi: {e}")
        raise
