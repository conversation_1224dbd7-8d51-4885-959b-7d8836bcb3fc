import logging
from typing import Any, Dict, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def compute_basic_arithmetic(num1: float, num2: float, operation: str) -> float:
    """
    Performs basic arithmetic operations (addition, subtraction, multiplication, division) on two numbers.

    Args:
        num1: The first number in the arithmetic operation.
        num2: The second number in the arithmetic operation.
        operation: The arithmetic operation to perform. Must be one of: 'add', 'subtract', 'multiply', 'divide'.

    Returns:
        float: The result of the arithmetic operation.

    Raises:
        ValueError: If the operation is not one of the supported operations.
        ZeroDivisionError: If division by zero is attempted.
    """
    try:
        logger.info(f"Performing operation: {operation} on numbers {num1} and {num2}")

        if operation == "add":
            result = num1 + num2
        elif operation == "subtract":
            result = num1 - num2
        elif operation == "multiply":
            result = num1 * num2
        elif operation == "divide":
            if num2 == 0:
                logger.error("Division by zero attempted")
                raise ZeroDivisionError("Cannot divide by zero")
            result = num1 / num2
        else:
            logger.error(f"Invalid operation: {operation}")
            raise ValueError(
                f"Invalid operation: {operation}. Must be one of: 'add', 'subtract', 'multiply', 'divide'"
            )

        logger.info(f"Operation {operation} completed successfully. Result: {result}")
        return result

    except ZeroDivisionError as e:
        logger.error(f"ZeroDivisionError: {e}")
        raise
    except ValueError as e:
        logger.error(f"ValueError: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise
