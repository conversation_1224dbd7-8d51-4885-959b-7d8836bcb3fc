"""Workflow generation system for creating dynamic LangGraph workflows."""

import json
from typing import Any, Dict, List, Optional, Tuple

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langgraph.graph import StateGraph, END
from openai import OpenAI
from pydantic import BaseModel, Field

from ..core.ability_manager import AbilityManager
from ..utils.config import config
from ..utils.decorators import retry
from ..utils.logger import logger


class WorkflowStep(BaseModel):
    """Represents a single step in a workflow."""
    step_id: str
    name: str
    description: str
    agent_type: str
    prompt_template: str
    inputs: List[str] = Field(default_factory=list)
    outputs: List[str] = Field(default_factory=list)
    dependencies: List[str] = Field(default_factory=list)
    capabilities_required: List[str] = Field(default_factory=list)


class WorkflowDefinition(BaseModel):
    """Complete workflow definition."""
    workflow_id: str
    name: str
    description: str
    steps: List[WorkflowStep]
    entry_point: str
    success_criteria: List[str] = Field(default_factory=list)


class WorkflowGenerator:
    """Generates dynamic workflows based on task requirements."""
    
    def __init__(self, ability_manager: Optional[AbilityManager] = None):
        """Initialize the workflow generator.
        
        Args:
            ability_manager: Optional ability manager instance
        """
        self.client = OpenAI(
            api_key=config.deepseek_api_key,
            base_url=config.deepseek_base_url
        )
        self.ability_manager = ability_manager or AbilityManager()
        logger.info("Initialized WorkflowGenerator")
    
    @retry()
    def generate_workflow(
        self,
        task_description: str,
        task_analysis: Dict[str, Any],
        available_capabilities: List[str]
    ) -> WorkflowDefinition:
        """Generate a workflow for a given task.
        
        Args:
            task_description: Description of the task
            task_analysis: Analysis results from task breakdown
            available_capabilities: List of available system capabilities
            
        Returns:
            Generated workflow definition
        """
        logger.info(f"Generating workflow for task: {task_description}")
        
        # 生成工作流结构
        workflow_structure = self._generate_workflow_structure(
            task_description, task_analysis, available_capabilities
        )
        
        # 为每个步骤生成详细的prompt
        steps = []
        for step_data in workflow_structure["steps"]:
            prompt = self._generate_step_prompt(step_data, task_analysis)
            
            step = WorkflowStep(
                step_id=step_data["step_id"],
                name=step_data["name"],
                description=step_data["description"],
                agent_type=step_data.get("agent_type", "general"),
                prompt_template=prompt,
                inputs=step_data.get("inputs", []),
                outputs=step_data.get("outputs", []),
                dependencies=step_data.get("dependencies", []),
                capabilities_required=step_data.get("capabilities_required", [])
            )
            steps.append(step)
        
        workflow = WorkflowDefinition(
            workflow_id=workflow_structure["workflow_id"],
            name=workflow_structure["name"],
            description=workflow_structure["description"],
            steps=steps,
            entry_point=workflow_structure["entry_point"],
            success_criteria=workflow_structure.get("success_criteria", [])
        )
        
        logger.info(f"Generated workflow with {len(steps)} steps")
        return workflow
    
    def _generate_workflow_structure(
        self,
        task_description: str,
        task_analysis: Dict[str, Any],
        available_capabilities: List[str]
    ) -> Dict[str, Any]:
        """Generate the high-level workflow structure.
        
        Args:
            task_description: Task description
            task_analysis: Task analysis results
            available_capabilities: Available capabilities
            
        Returns:
            Workflow structure dictionary
        """
        prompt = f"""Design a workflow structure for the following task:

Task: {task_description}

Task Analysis:
- Type: {task_analysis.get('task_type', 'unknown')}
- Category: {task_analysis.get('category', 'unknown')}
- Sub-tasks: {task_analysis.get('sub_tasks', [])}
- Required capabilities: {task_analysis.get('required_capabilities', [])}
- Complexity: {task_analysis.get('complexity', 5)}/10

Available System Capabilities:
{', '.join(available_capabilities)}

Design a workflow with the following structure:
1. Break the task into logical steps that can be executed by different agents
2. Each step should have clear inputs, outputs, and dependencies
3. Identify what type of agent is best suited for each step
4. Ensure the workflow is efficient and follows best practices

Provide a JSON response with:
{{
    "workflow_id": "unique_workflow_id",
    "name": "workflow_name",
    "description": "workflow_description",
    "entry_point": "first_step_id",
    "steps": [
        {{
            "step_id": "step_1",
            "name": "step_name",
            "description": "what this step does",
            "agent_type": "type_of_agent_needed",
            "inputs": ["input1", "input2"],
            "outputs": ["output1", "output2"],
            "dependencies": ["previous_step_ids"],
            "capabilities_required": ["capability1", "capability2"]
        }}
    ],
    "success_criteria": ["criterion1", "criterion2"]
}}

Respond only with the JSON structure."""
        
        try:
            response = self.client.chat.completions.create(
                model=config.default_model,
                messages=[
                    {"role": "system", "content": "You are a workflow design expert. Create efficient, logical workflows for complex tasks."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=2000
            )
            
            workflow_structure = json.loads(response.choices[0].message.content)
            logger.info("Generated workflow structure successfully")
            return workflow_structure
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse workflow structure JSON: {e}")
            # 返回默认结构
            return self._create_default_workflow_structure(task_description, task_analysis)
        except Exception as e:
            logger.error(f"Failed to generate workflow structure: {e}")
            raise
    
    def _generate_step_prompt(
        self,
        step_data: Dict[str, Any],
        task_analysis: Dict[str, Any]
    ) -> str:
        """Generate a detailed prompt for a workflow step.
        
        Args:
            step_data: Step information
            task_analysis: Overall task analysis
            
        Returns:
            Generated prompt template
        """
        prompt_generation_request = f"""Create a detailed prompt template for a workflow step:

Step Information:
- Name: {step_data['name']}
- Description: {step_data['description']}
- Agent Type: {step_data.get('agent_type', 'general')}
- Inputs: {step_data.get('inputs', [])}
- Outputs: {step_data.get('outputs', [])}
- Required Capabilities: {step_data.get('capabilities_required', [])}

Overall Task Context:
- Task Type: {task_analysis.get('task_type', 'unknown')}
- Complexity: {task_analysis.get('complexity', 5)}/10
- Success Criteria: {task_analysis.get('success_criteria', [])}

Create a prompt template that:
1. Clearly defines the agent's role and responsibilities for this step
2. Provides context about the overall task
3. Specifies the expected inputs and how to use them
4. Clearly describes the expected outputs and format
5. Includes any constraints or guidelines
6. Uses placeholders like {{input_name}} for dynamic values

The prompt should be professional, clear, and actionable.

Respond with only the prompt template, no additional formatting or explanations."""
        
        try:
            response = self.client.chat.completions.create(
                model=config.default_model,
                messages=[
                    {"role": "system", "content": "You are an expert at creating clear, effective prompts for AI agents."},
                    {"role": "user", "content": prompt_generation_request}
                ],
                temperature=0.4,
                max_tokens=1000
            )
            
            prompt_template = response.choices[0].message.content.strip()
            logger.debug(f"Generated prompt for step: {step_data['name']}")
            return prompt_template
            
        except Exception as e:
            logger.error(f"Failed to generate prompt for step {step_data['name']}: {e}")
            # 返回默认prompt
            return self._create_default_step_prompt(step_data)
    
    def _create_default_workflow_structure(
        self,
        task_description: str,
        task_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a default workflow structure when generation fails.
        
        Args:
            task_description: Task description
            task_analysis: Task analysis
            
        Returns:
            Default workflow structure
        """
        return {
            "workflow_id": f"workflow_{hash(task_description) % 10000}",
            "name": f"Workflow for {task_description[:50]}",
            "description": f"Auto-generated workflow for: {task_description}",
            "entry_point": "step_1",
            "steps": [
                {
                    "step_id": "step_1",
                    "name": "Task Analysis",
                    "description": "Analyze and understand the task requirements",
                    "agent_type": "analyzer",
                    "inputs": ["task_description"],
                    "outputs": ["analysis_result"],
                    "dependencies": [],
                    "capabilities_required": ["task_analysis"]
                },
                {
                    "step_id": "step_2", 
                    "name": "Task Execution",
                    "description": "Execute the main task based on analysis",
                    "agent_type": "executor",
                    "inputs": ["analysis_result"],
                    "outputs": ["execution_result"],
                    "dependencies": ["step_1"],
                    "capabilities_required": task_analysis.get("required_capabilities", [])
                },
                {
                    "step_id": "step_3",
                    "name": "Result Validation",
                    "description": "Validate and format the final result",
                    "agent_type": "validator",
                    "inputs": ["execution_result"],
                    "outputs": ["final_result"],
                    "dependencies": ["step_2"],
                    "capabilities_required": ["result_validation"]
                }
            ],
            "success_criteria": ["task_completed", "result_validated"]
        }
    
    def _create_default_step_prompt(self, step_data: Dict[str, Any]) -> str:
        """Create a default prompt template for a step.
        
        Args:
            step_data: Step information
            
        Returns:
            Default prompt template
        """
        return f"""You are an AI agent responsible for: {step_data['name']}

Task: {step_data['description']}

Inputs you will receive:
{chr(10).join(f"- {inp}: {{{{ {inp} }}}}" for inp in step_data.get('inputs', []))}

Your responsibilities:
1. Process the provided inputs carefully
2. Apply your capabilities to complete the task
3. Ensure your output meets the requirements

Expected outputs:
{chr(10).join(f"- {out}" for out in step_data.get('outputs', []))}

Please complete this task step by step and provide clear, actionable results."""
    
    def create_langgraph_workflow(self, workflow_def: WorkflowDefinition) -> StateGraph:
        """Create a LangGraph StateGraph from workflow definition.
        
        Args:
            workflow_def: Workflow definition
            
        Returns:
            Configured StateGraph
        """
        # 定义状态结构
        class WorkflowState(dict):
            """Workflow state that can hold any key-value pairs."""
            pass
        
        # 创建图
        workflow = StateGraph(WorkflowState)
        
        # 为每个步骤创建节点
        for step in workflow_def.steps:
            node_function = self._create_step_function(step)
            workflow.add_node(step.step_id, node_function)
        
        # 添加边（依赖关系）
        for step in workflow_def.steps:
            if not step.dependencies:
                # 入口点
                workflow.set_entry_point(step.step_id)
            else:
                # 添加从依赖步骤到当前步骤的边
                for dep in step.dependencies:
                    workflow.add_edge(dep, step.step_id)
        
        # 找到最后的步骤并连接到END
        final_steps = []
        for step in workflow_def.steps:
            is_final = True
            for other_step in workflow_def.steps:
                if step.step_id in other_step.dependencies:
                    is_final = False
                    break
            if is_final:
                final_steps.append(step.step_id)
        
        for final_step in final_steps:
            workflow.add_edge(final_step, END)
        
        logger.info(f"Created LangGraph workflow with {len(workflow_def.steps)} nodes")
        return workflow.compile()
    
    def _create_step_function(self, step: WorkflowStep):
        """Create a function for a workflow step.
        
        Args:
            step: Step definition
            
        Returns:
            Function that can be used as a LangGraph node
        """
        def step_function(state: Dict[str, Any]) -> Dict[str, Any]:
            """Execute a workflow step.
            
            Args:
                state: Current workflow state
                
            Returns:
                Updated state
            """
            logger.info(f"Executing step: {step.name}")
            
            try:
                # 准备输入数据
                inputs = {}
                for input_name in step.inputs:
                    if input_name in state:
                        inputs[input_name] = state[input_name]
                    else:
                        logger.warning(f"Missing input '{input_name}' for step {step.name}")
                
                # 格式化prompt
                formatted_prompt = step.prompt_template
                for key, value in inputs.items():
                    formatted_prompt = formatted_prompt.replace(f"{{{{{key}}}}}", str(value))
                
                # 调用LLM
                response = self.client.chat.completions.create(
                    model=config.default_model,
                    messages=[
                        {"role": "user", "content": formatted_prompt}
                    ],
                    temperature=0.7,
                    max_tokens=1500
                )
                
                result = response.choices[0].message.content
                
                # 更新状态
                state[f"{step.step_id}_result"] = result
                for output_name in step.outputs:
                    state[output_name] = result  # 简化处理，实际可能需要解析
                
                logger.info(f"Completed step: {step.name}")
                return state
                
            except Exception as e:
                logger.error(f"Step {step.name} failed: {e}")
                state[f"{step.step_id}_error"] = str(e)
                return state
        
        return step_function
