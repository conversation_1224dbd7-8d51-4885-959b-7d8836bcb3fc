#!/usr/bin/env python3
"""Test script to verify API connection and basic functionality."""

import asyncio
import json
from openai import OpenAI

from src.self_expanding_agent.utils.config import config
from src.self_expanding_agent.core.master_agent import MasterAgent


def test_api_connection():
    """Test direct API connection."""
    print("🔍 测试API连接...")
    
    try:
        client = OpenAI(
            api_key=config.deepseek_api_key,
            base_url=config.deepseek_base_url
        )
        
        response = client.chat.completions.create(
            model=config.default_model,
            messages=[
                {"role": "user", "content": "请回答：1+1等于多少？"}
            ],
            max_tokens=100,
            temperature=0.1
        )
        
        print(f"✅ API连接成功！")
        print(f"📝 响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False


def test_json_response():
    """Test JSON response parsing."""
    print("\n🔍 测试JSON响应解析...")
    
    try:
        client = OpenAI(
            api_key=config.deepseek_api_key,
            base_url=config.deepseek_base_url
        )
        
        response = client.chat.completions.create(
            model=config.default_model,
            messages=[
                {"role": "user", "content": """请以JSON格式回答以下问题，不要包含任何其他文本：
{
  "task_type": "数学计算",
  "complexity": 1,
  "result": "1+1的答案"
}"""}
            ],
            max_tokens=200,
            temperature=0.1
        )
        
        content = response.choices[0].message.content.strip()
        print(f"📝 原始响应: {content}")
        
        # 尝试解析JSON
        try:
            parsed = json.loads(content)
            print(f"✅ JSON解析成功: {parsed}")
            return True
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"尝试清理响应...")
            
            # 尝试提取JSON部分
            if '{' in content and '}' in content:
                start = content.find('{')
                end = content.rfind('}') + 1
                json_part = content[start:end]
                print(f"提取的JSON部分: {json_part}")
                
                try:
                    parsed = json.loads(json_part)
                    print(f"✅ 清理后JSON解析成功: {parsed}")
                    return True
                except json.JSONDecodeError:
                    print(f"❌ 清理后仍无法解析JSON")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_master_agent():
    """Test master agent functionality."""
    print("\n🔍 测试主智能体功能...")

    try:
        agent = MasterAgent()
        print("✅ 主智能体初始化成功")

        # 测试简单任务
        task = "计算2+3的结果"
        print(f"🎯 测试任务: {task}")

        result = agent.process_task(task)
        print(f"📊 处理结果: {result}")

        return True

    except Exception as e:
        print(f"❌ 主智能体测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 自扩展智能体系统 - API连接测试")
    print("=" * 50)
    
    # 测试API连接
    api_ok = test_api_connection()
    
    # 测试JSON响应
    json_ok = test_json_response()
    
    # 测试主智能体
    if api_ok and json_ok:
        agent_ok = test_master_agent()
    else:
        print("\n⚠️ 跳过主智能体测试（API或JSON测试失败）")
        agent_ok = False
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  API连接: {'✅' if api_ok else '❌'}")
    print(f"  JSON解析: {'✅' if json_ok else '❌'}")
    print(f"  主智能体: {'✅' if agent_ok else '❌'}")
    
    if api_ok and json_ok and agent_ok:
        print("\n🎉 所有测试通过！系统功能正常。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")


if __name__ == "__main__":
    main()
