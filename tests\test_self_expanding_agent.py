"""Tests for the self-expanding agent system."""

import os
import sys
import tempfile
import unittest
from pathlib import Path
from unittest.mock import Mock, patch

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from self_expanding_agent.core.ability_manager import Ability, AbilityManager
from self_expanding_agent.core.base_agent import BaseAgent
from self_expanding_agent.core.code_generator import CodeGenerator
from self_expanding_agent.core.master_agent import MasterAgent
from self_expanding_agent.extensions.api_discovery import APIDiscovery, APIService, APIEndpoint
from self_expanding_agent.extensions.dependency_manager import DependencyManager
from self_expanding_agent.extensions.module_integrator import ModuleIntegrator
from self_expanding_agent.workflow.workflow_generator import WorkflowGenerator
from self_expanding_agent.workflow.dynamic_workflow import DynamicWorkflow


class TestAbilityManager(unittest.TestCase):
    """测试能力管理器."""
    
    def setUp(self):
        """设置测试环境."""
        self.temp_dir = tempfile.mkdtemp()
        self.ability_manager = AbilityManager(db_path=f"{self.temp_dir}/test.db")
    
    def test_register_ability(self):
        """测试注册能力."""
        ability = Ability(
            name="test_ability",
            description="Test ability",
            category="test"
        )
        
        self.ability_manager.register_ability(ability)
        
        # 验证能力已注册
        retrieved = self.ability_manager.get_ability("test_ability")
        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.name, "test_ability")
        self.assertEqual(retrieved.description, "Test ability")
    
    def test_list_abilities(self):
        """测试列出能力."""
        # 注册多个能力
        abilities = [
            Ability(name="ability1", description="First ability", category="test"),
            Ability(name="ability2", description="Second ability", category="test"),
            Ability(name="ability3", description="Third ability", category="other")
        ]
        
        for ability in abilities:
            self.ability_manager.register_ability(ability)
        
        # 测试列出所有能力
        all_abilities = self.ability_manager.list_abilities()
        self.assertEqual(len(all_abilities), 3)
        
        # 测试按类别过滤
        test_abilities = self.ability_manager.list_abilities(category="test")
        self.assertEqual(len(test_abilities), 2)
    
    def test_search_abilities(self):
        """测试搜索能力."""
        abilities = [
            Ability(name="text_analysis", description="Analyze text content", category="nlp"),
            Ability(name="image_processing", description="Process images", category="cv"),
            Ability(name="data_analysis", description="Analyze data patterns", category="data")
        ]
        
        for ability in abilities:
            self.ability_manager.register_ability(ability)
        
        # 搜索包含"analysis"的能力
        results = self.ability_manager.search_abilities("analysis")
        self.assertEqual(len(results), 2)
        
        # 搜索特定类别
        results = self.ability_manager.search_abilities("image")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].name, "image_processing")


class TestCodeGenerator(unittest.TestCase):
    """测试代码生成器."""
    
    def setUp(self):
        """设置测试环境."""
        # 模拟LLM客户端
        self.mock_client = Mock()
        self.code_generator = CodeGenerator()
        self.code_generator.client = self.mock_client
    
    def test_validate_code(self):
        """测试代码验证."""
        # 测试有效代码
        valid_code = """
def hello_world():
    return "Hello, World!"
"""
        is_valid, error = self.code_generator.validate_code(valid_code)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # 测试无效代码
        invalid_code = """
def invalid_function(
    # 缺少闭合括号
    return "Invalid"
"""
        is_valid, error = self.code_generator.validate_code(invalid_code)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
    
    def test_format_code(self):
        """测试代码格式化."""
        unformatted_code = """
def   messy_function(  x,y  ):
    if x>y:
        return x
    else:
        return y
"""

        formatted = self.code_generator._format_code(unformatted_code)

        # 验证代码已格式化（包含适当的空格）
        self.assertIn("def messy_function(x, y):", formatted)
        self.assertIn("if x > y:", formatted)


class TestAPIDiscovery(unittest.TestCase):
    """测试API发现器."""
    
    def setUp(self):
        """设置测试环境."""
        self.api_discovery = APIDiscovery()
    
    def test_predefined_apis(self):
        """测试预定义API."""
        # 检查是否加载了预定义API
        apis = self.api_discovery.list_discovered_apis()
        self.assertGreater(len(apis), 0)
        
        # 检查特定API
        weather_api = self.api_discovery.get_api_service("OpenWeatherMap")
        self.assertIsNotNone(weather_api)
        self.assertEqual(weather_api.name, "OpenWeatherMap")
        self.assertTrue(weather_api.api_key_required)
    
    def test_find_relevant_apis(self):
        """测试查找相关API."""
        # 查找天气相关API
        relevant_apis = self.api_discovery._find_relevant_existing_apis("weather")
        
        # 应该找到OpenWeatherMap API
        weather_api_found = any(api.name == "OpenWeatherMap" for api in relevant_apis)
        self.assertTrue(weather_api_found)
    
    @patch('requests.request')
    def test_api_endpoint_testing(self, mock_request):
        """测试API端点测试."""
        # 模拟成功的API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success", "data": "test"}
        mock_request.return_value = mock_response
        
        # 创建测试API
        api_service = APIService(
            name="TestAPI",
            base_url="https://api.test.com",
            description="Test API",
            category="test",
            endpoints=[
                APIEndpoint(
                    name="test_endpoint",
                    url="/test",
                    method="GET",
                    description="Test endpoint"
                )
            ]
        )
        
        # 测试端点
        success, response = self.api_discovery.test_api_endpoint(
            api_service, "test_endpoint"
        )
        
        self.assertTrue(success)
        self.assertIsNotNone(response)


class TestDependencyManager(unittest.TestCase):
    """测试依赖管理器."""
    
    def setUp(self):
        """设置测试环境."""
        self.dep_manager = DependencyManager()
    
    def test_analyze_code_dependencies(self):
        """测试代码依赖分析."""
        code = """
import requests
import pandas as pd
from sklearn.model_selection import train_test_split
import os  # 标准库，应该被过滤
import json  # 标准库，应该被过滤
"""
        
        dependencies = self.dep_manager.analyze_code_dependencies(code)
        
        # 应该包含外部依赖
        self.assertIn("requests", dependencies)
        self.assertIn("pandas", dependencies)
        self.assertIn("sklearn", dependencies)
        
        # 不应该包含标准库
        self.assertNotIn("os", dependencies)
        self.assertNotIn("json", dependencies)
    
    def test_check_dependencies(self):
        """测试依赖检查."""
        # 测试已知存在的包（假设requests已安装）
        status = self.dep_manager.check_dependencies(["sys", "os"])
        
        # sys和os是内置模块，但在installed_packages中可能不存在
        # 这个测试主要验证函数不会崩溃
        self.assertIsInstance(status, dict)
    
    def test_suggest_alternatives(self):
        """测试包替代建议."""
        alternatives = self.dep_manager.suggest_alternatives("cv2")
        self.assertIn("opencv-python", alternatives)
        
        alternatives = self.dep_manager.suggest_alternatives("PIL")
        self.assertIn("Pillow", alternatives)


class TestModuleIntegrator(unittest.TestCase):
    """测试模块集成器."""
    
    def setUp(self):
        """设置测试环境."""
        self.temp_dir = tempfile.mkdtemp()
        self.integrator = ModuleIntegrator()
        
        # 使用临时目录
        from pathlib import Path
        self.integrator.ability_manager.db_path = Path(f"{self.temp_dir}/test.db")
        self.integrator.ability_manager._init_database()
    
    def test_save_and_load_module(self):
        """测试保存和加载模块."""
        test_code = '''
def test_function():
    """测试函数."""
    return "Hello from test module!"

class TestClass:
    """测试类."""
    def __init__(self):
        self.value = 42
'''
        
        # 保存代码
        module_path = self.integrator._save_code_to_file(test_code, "test_module")
        self.assertTrue(module_path.exists())
        
        # 加载模块
        loaded_module = self.integrator._load_module_from_file(module_path, "test_module")
        self.assertIsNotNone(loaded_module)
        
        # 验证模块内容
        self.assertTrue(hasattr(loaded_module, "test_function"))
        self.assertTrue(hasattr(loaded_module, "TestClass"))
        
        # 测试函数调用
        result = loaded_module.test_function()
        self.assertEqual(result, "Hello from test module!")
    
    def test_validate_module(self):
        """测试模块验证."""
        # 创建一个简单的模块
        test_code = '''
def capability_function():
    return "capability result"

class CapabilityClass:
    pass
'''
        
        module_path = self.integrator._save_code_to_file(test_code, "capability_module")
        loaded_module = self.integrator._load_module_from_file(module_path, "capability_module")
        
        # 验证模块
        validation_result = self.integrator._validate_module(loaded_module, "capability")
        
        self.assertTrue(validation_result["valid"])
        self.assertIn("capability_function", validation_result["functions"])
        self.assertIn("CapabilityClass", validation_result["classes"])


class TestWorkflowGenerator(unittest.TestCase):
    """测试工作流生成器."""
    
    def setUp(self):
        """设置测试环境."""
        self.workflow_generator = WorkflowGenerator()
        
        # 模拟LLM客户端
        self.mock_client = Mock()
        self.workflow_generator.client = self.mock_client
    
    def test_create_default_workflow_structure(self):
        """测试创建默认工作流结构."""
        task_description = "Process data and generate report"
        task_analysis = {
            "task_type": "data_processing",
            "complexity": 5,
            "required_capabilities": ["data_processing", "report_generation"]
        }
        
        workflow_structure = self.workflow_generator._create_default_workflow_structure(
            task_description, task_analysis
        )
        
        self.assertIn("workflow_id", workflow_structure)
        self.assertIn("steps", workflow_structure)
        self.assertGreater(len(workflow_structure["steps"]), 0)
        
        # 验证步骤结构
        first_step = workflow_structure["steps"][0]
        self.assertIn("step_id", first_step)
        self.assertIn("name", first_step)
        self.assertIn("description", first_step)


class TestIntegration(unittest.TestCase):
    """集成测试."""
    
    def setUp(self):
        """设置测试环境."""
        self.temp_dir = tempfile.mkdtemp()
        
        # 设置环境变量（如果没有的话）
        if not os.getenv("DEEPSEEK_API_KEY"):
            os.environ["DEEPSEEK_API_KEY"] = "test-key"
    
    def test_master_agent_initialization(self):
        """测试主智能体初始化."""
        master_agent = MasterAgent()
        
        self.assertEqual(master_agent.state.name, "MasterAgent")
        self.assertIsNotNone(master_agent.ability_manager)
        self.assertIsNotNone(master_agent.code_generator)
        
        # 检查核心能力是否已注册
        abilities = master_agent.ability_manager.list_abilities()
        ability_names = [ability.name for ability in abilities]
        
        self.assertIn("task_analysis", ability_names)
        self.assertIn("capability_assessment", ability_names)
    
    def test_system_status(self):
        """测试系统状态获取."""
        master_agent = MasterAgent()
        status = master_agent.get_system_status()
        
        self.assertIn("master_agent_status", status)
        self.assertIn("total_abilities", status)
        self.assertIn("system_health", status)
        
        self.assertGreater(status["total_abilities"], 0)


def run_tests():
    """运行所有测试."""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestAbilityManager,
        TestCodeGenerator,
        TestAPIDiscovery,
        TestDependencyManager,
        TestModuleIntegrator,
        TestWorkflowGenerator,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    print("运行自扩展智能体系统测试...")
    print("=" * 50)
    
    success = run_tests()
    
    print("\n" + "=" * 50)
    if success:
        print("所有测试通过！✅")
    else:
        print("部分测试失败！❌")
        sys.exit(1)
