"""Dynamic workflow execution system using LangGraph."""

import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from langgraph.graph import StateGraph
from pydantic import BaseModel, Field

from .workflow_generator import WorkflowDefinition, WorkflowGenerator
from ..core.ability_manager import AbilityManager
from ..utils.logger import logger


class WorkflowExecution(BaseModel):
    """Represents a workflow execution instance."""
    execution_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    workflow_id: str
    status: str = "pending"  # pending, running, completed, failed
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    current_step: Optional[str] = None
    state: Dict[str, Any] = Field(default_factory=dict)
    results: Dict[str, Any] = Field(default_factory=dict)
    errors: List[str] = Field(default_factory=list)


class DynamicWorkflow:
    """Manages dynamic workflow creation and execution."""
    
    def __init__(self, ability_manager: Optional[AbilityManager] = None):
        """Initialize the dynamic workflow system.
        
        Args:
            ability_manager: Optional ability manager instance
        """
        self.ability_manager = ability_manager or AbilityManager()
        self.workflow_generator = WorkflowGenerator(self.ability_manager)
        self.active_executions: Dict[str, WorkflowExecution] = {}
        self.workflow_cache: Dict[str, WorkflowDefinition] = {}
        
        logger.info("Initialized DynamicWorkflow system")
    
    def create_and_execute_workflow(
        self,
        task_description: str,
        task_analysis: Dict[str, Any],
        initial_state: Optional[Dict[str, Any]] = None
    ) -> WorkflowExecution:
        """Create and execute a workflow for a given task.
        
        Args:
            task_description: Description of the task
            task_analysis: Analysis results from task breakdown
            initial_state: Initial state for workflow execution
            
        Returns:
            Workflow execution instance
        """
        logger.info(f"Creating workflow for task: {task_description}")
        
        # 获取可用能力
        available_capabilities = [
            ability.name for ability in self.ability_manager.list_abilities()
        ]
        
        # 生成工作流定义
        workflow_def = self.workflow_generator.generate_workflow(
            task_description=task_description,
            task_analysis=task_analysis,
            available_capabilities=available_capabilities
        )
        
        # 缓存工作流定义
        self.workflow_cache[workflow_def.workflow_id] = workflow_def
        
        # 创建执行实例
        execution = WorkflowExecution(
            workflow_id=workflow_def.workflow_id,
            state=initial_state or {"task_description": task_description}
        )
        
        self.active_executions[execution.execution_id] = execution
        
        # 执行工作流
        self._execute_workflow(execution, workflow_def)
        
        return execution
    
    def _execute_workflow(
        self,
        execution: WorkflowExecution,
        workflow_def: WorkflowDefinition
    ) -> None:
        """Execute a workflow.
        
        Args:
            execution: Workflow execution instance
            workflow_def: Workflow definition
        """
        try:
            execution.status = "running"
            execution.start_time = datetime.now()
            
            logger.info(f"Starting workflow execution: {execution.execution_id}")
            
            # 创建LangGraph工作流
            langgraph_workflow = self.workflow_generator.create_langgraph_workflow(workflow_def)
            
            # 执行工作流
            final_state = langgraph_workflow.invoke(execution.state)
            
            # 更新执行结果
            execution.state.update(final_state)
            execution.status = "completed"
            execution.end_time = datetime.now()
            
            # 提取结果
            execution.results = self._extract_results(final_state, workflow_def)
            
            logger.info(f"Workflow execution completed: {execution.execution_id}")
            
        except Exception as e:
            execution.status = "failed"
            execution.end_time = datetime.now()
            execution.errors.append(str(e))
            
            logger.error(f"Workflow execution failed: {execution.execution_id}, Error: {e}")
    
    async def execute_workflow_async(
        self,
        execution: WorkflowExecution,
        workflow_def: WorkflowDefinition
    ) -> None:
        """Execute a workflow asynchronously.
        
        Args:
            execution: Workflow execution instance
            workflow_def: Workflow definition
        """
        try:
            execution.status = "running"
            execution.start_time = datetime.now()
            
            logger.info(f"Starting async workflow execution: {execution.execution_id}")
            
            # 创建LangGraph工作流
            langgraph_workflow = self.workflow_generator.create_langgraph_workflow(workflow_def)
            
            # 异步执行工作流
            final_state = await asyncio.get_event_loop().run_in_executor(
                None, langgraph_workflow.invoke, execution.state
            )
            
            # 更新执行结果
            execution.state.update(final_state)
            execution.status = "completed"
            execution.end_time = datetime.now()
            
            # 提取结果
            execution.results = self._extract_results(final_state, workflow_def)
            
            logger.info(f"Async workflow execution completed: {execution.execution_id}")
            
        except Exception as e:
            execution.status = "failed"
            execution.end_time = datetime.now()
            execution.errors.append(str(e))
            
            logger.error(f"Async workflow execution failed: {execution.execution_id}, Error: {e}")
    
    def _extract_results(
        self,
        final_state: Dict[str, Any],
        workflow_def: WorkflowDefinition
    ) -> Dict[str, Any]:
        """Extract meaningful results from the final workflow state.
        
        Args:
            final_state: Final state after workflow execution
            workflow_def: Workflow definition
            
        Returns:
            Extracted results
        """
        results = {}
        
        # 提取每个步骤的结果
        for step in workflow_def.steps:
            step_result_key = f"{step.step_id}_result"
            if step_result_key in final_state:
                results[step.name] = final_state[step_result_key]
            
            # 提取步骤的输出
            for output in step.outputs:
                if output in final_state:
                    results[output] = final_state[output]
        
        # 提取最终结果
        if "final_result" in final_state:
            results["final_result"] = final_state["final_result"]
        
        return results
    
    def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """Get the status of a workflow execution.
        
        Args:
            execution_id: Execution ID
            
        Returns:
            Workflow execution instance or None if not found
        """
        return self.active_executions.get(execution_id)
    
    def list_active_executions(self) -> List[WorkflowExecution]:
        """List all active workflow executions.
        
        Returns:
            List of active executions
        """
        return list(self.active_executions.values())
    
    def cancel_execution(self, execution_id: str) -> bool:
        """Cancel a workflow execution.
        
        Args:
            execution_id: Execution ID
            
        Returns:
            True if successfully cancelled
        """
        execution = self.active_executions.get(execution_id)
        if execution and execution.status == "running":
            execution.status = "cancelled"
            execution.end_time = datetime.now()
            execution.errors.append("Execution cancelled by user")
            
            logger.info(f"Cancelled workflow execution: {execution_id}")
            return True
        
        return False
    
    def cleanup_completed_executions(self, max_age_hours: int = 24) -> int:
        """Clean up old completed executions.
        
        Args:
            max_age_hours: Maximum age in hours for keeping executions
            
        Returns:
            Number of executions cleaned up
        """
        current_time = datetime.now()
        cleaned_count = 0
        
        executions_to_remove = []
        for execution_id, execution in self.active_executions.items():
            if execution.end_time:
                age_hours = (current_time - execution.end_time).total_seconds() / 3600
                if age_hours > max_age_hours:
                    executions_to_remove.append(execution_id)
        
        for execution_id in executions_to_remove:
            del self.active_executions[execution_id]
            cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old workflow executions")
        
        return cleaned_count
    
    def get_workflow_definition(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get a cached workflow definition.
        
        Args:
            workflow_id: Workflow ID
            
        Returns:
            Workflow definition or None if not found
        """
        return self.workflow_cache.get(workflow_id)
    
    def create_workflow_from_template(
        self,
        template_name: str,
        parameters: Dict[str, Any]
    ) -> Optional[WorkflowDefinition]:
        """Create a workflow from a predefined template.
        
        Args:
            template_name: Name of the template
            parameters: Template parameters
            
        Returns:
            Generated workflow definition or None if template not found
        """
        # 这里可以实现预定义的工作流模板
        templates = {
            "data_analysis": self._create_data_analysis_template,
            "web_scraping": self._create_web_scraping_template,
            "content_generation": self._create_content_generation_template
        }
        
        template_func = templates.get(template_name)
        if template_func:
            return template_func(parameters)
        
        logger.warning(f"Template not found: {template_name}")
        return None
    
    def _create_data_analysis_template(self, parameters: Dict[str, Any]) -> WorkflowDefinition:
        """Create a data analysis workflow template.
        
        Args:
            parameters: Template parameters
            
        Returns:
            Workflow definition
        """
        # 这是一个示例模板实现
        from .workflow_generator import WorkflowStep
        
        steps = [
            WorkflowStep(
                step_id="data_loading",
                name="Data Loading",
                description="Load and validate input data",
                agent_type="data_loader",
                prompt_template="Load the data from {data_source} and validate its structure.",
                inputs=["data_source"],
                outputs=["loaded_data"],
                capabilities_required=["data_loading", "data_validation"]
            ),
            WorkflowStep(
                step_id="data_analysis",
                name="Data Analysis",
                description="Perform statistical analysis on the data",
                agent_type="data_analyst",
                prompt_template="Analyze the loaded data: {loaded_data}. Provide statistical insights.",
                inputs=["loaded_data"],
                outputs=["analysis_results"],
                dependencies=["data_loading"],
                capabilities_required=["statistical_analysis", "data_visualization"]
            ),
            WorkflowStep(
                step_id="report_generation",
                name="Report Generation",
                description="Generate a comprehensive analysis report",
                agent_type="report_generator",
                prompt_template="Create a report based on analysis: {analysis_results}",
                inputs=["analysis_results"],
                outputs=["final_report"],
                dependencies=["data_analysis"],
                capabilities_required=["report_generation", "data_visualization"]
            )
        ]
        
        return WorkflowDefinition(
            workflow_id=f"data_analysis_{uuid.uuid4().hex[:8]}",
            name="Data Analysis Workflow",
            description="Automated data analysis and reporting workflow",
            steps=steps,
            entry_point="data_loading",
            success_criteria=["data_loaded", "analysis_completed", "report_generated"]
        )
    
    def _create_web_scraping_template(self, parameters: Dict[str, Any]) -> WorkflowDefinition:
        """Create a web scraping workflow template."""
        # 实现web scraping模板
        pass
    
    def _create_content_generation_template(self, parameters: Dict[str, Any]) -> WorkflowDefinition:
        """Create a content generation workflow template."""
        # 实现content generation模板
        pass
