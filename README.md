# Self-Expanding Agent System

一个能够在运行时自我扩展功能的智能体系统，使用 LangGraph 和 DeepSeek 构建。

## 特性

- 🤖 **自我扩展**: 智能体能够自动识别缺失的能力并生成相应的代码模块
- 🔄 **动态工作流**: 根据任务自动生成和调整 LangGraph 工作流
- 📝 **智能Prompt生成**: 自动生成优化的 prompt 用于不同的 agent
- 🔧 **API集成**: 自动发现和集成所需的外部 API
- 📦 **依赖管理**: 自动安装和管理项目依赖
- 🔐 **密钥管理**: 安全的 API 密钥管理系统

## 系统架构

```
核心智能体层
├── 主智能体 (Master Agent)
├── 工作流生成器 (Workflow Generator)
├── 代码生成器 (Code Generator)
└── 能力管理器 (Ability Manager)

LangGraph工作流层
├── 动态工作流 (Dynamic Workflow)
└── Agent 串联执行

功能扩展层
├── API发现器 (API Discovery)
├── 依赖管理器 (Dependency Manager)
├── 模块集成器 (Module Integrator)
└── 密钥管理器 (Key Manager)

存储层
├── Prompt存储 (Prompt Store)
├── 代码存储 (Code Store)
├── 能力存储 (Ability Store)
└── 元数据存储 (Metadata Store)
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

复制 `.env.example` 到 `.env` 并配置您的 API 密钥：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
```

### 3. 运行示例

```python
from self_expanding_agent import MasterAgent

# 创建主智能体
agent = MasterAgent()

# 执行任务
result = agent.execute_task("创建一个天气查询功能")
print(result)
```

## 核心组件

### MasterAgent
主智能体，负责任务分析、能力评估和决策制定。

### WorkflowGenerator
根据任务自动生成 LangGraph 工作流和相应的 prompt。

### CodeGenerator
当发现缺少能力时，自动生成所需的代码模块。

### AbilityManager
管理智能体的所有能力，包括注册、查询和更新。

## 开发

### 运行测试

```bash
pytest tests/
```

### 代码格式化

```bash
black src/
isort src/
```

## 许可证

MIT License
