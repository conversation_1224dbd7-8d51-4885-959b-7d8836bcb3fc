import logging
from typing import Any, Dict, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def perform_arithmetic_calculation(num1: float, num2: float, operation: str) -> float:
    """
    Performs basic arithmetic calculations (addition, subtraction, multiplication, division)
    based on the provided inputs and operation type.

    Args:
        num1: The first operand in the arithmetic operation.
        num2: The second operand in the arithmetic operation.
        operation: The type of arithmetic operation to perform. Supported values: 'add', 'subtract', 'multiply', 'divide'.

    Returns:
        float: The result of the arithmetic operation.

    Raises:
        ValueError: If the operation is not supported or if division by zero occurs.
        TypeError: If the input operands are not numeric.
    """
    try:
        logger.info(f"Performing {operation} operation with operands {num1} and {num2}")

        if operation == "add":
            result = num1 + num2
        elif operation == "subtract":
            result = num1 - num2
        elif operation == "multiply":
            result = num1 * num2
        elif operation == "divide":
            if num2 == 0:
                logger.error("Division by zero error")
                raise ValueError("Cannot divide by zero")
            result = num1 / num2
        else:
            logger.error(f"Unsupported operation: {operation}")
            raise ValueError(f"Unsupported operation: {operation}")

        logger.info(f"Operation {operation} completed successfully. Result: {result}")
        return result

    except TypeError as e:
        logger.error(f"Type error encountered: {e}")
        raise TypeError("Operands must be numeric")
    except Exception as e:
        logger.error(f"Unexpected error during arithmetic calculation: {e}")
        raise
