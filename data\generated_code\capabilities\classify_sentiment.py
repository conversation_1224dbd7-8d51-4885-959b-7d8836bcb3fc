import logging
from typing import Any, Dict, List, Optional

import nltk
from nltk.sentiment import SentimentIntensityAnalyzer
from textblob import TextBlob
from transformers import pipeline

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def classify_sentiment(
    text: str, model: Optional[str] = None, language: str = "en"
) -> Dict[str, Any]:
    """
    Analyzes the sentiment of a given text input and classifies it as positive, negative, or neutral.

    Args:
        text (str): The input text to be analyzed for sentiment.
        model (Optional[str]): The sentiment analysis model to use. Options: 'textblob', 'vader', 'transformers'.
                               Defaults to 'textblob' if not provided.
        language (str): The language of the input text. Defaults to 'en' (English).

    Returns:
        Dict[str, Any]: A dictionary containing the sentiment analysis result with keys:
                        - 'sentiment': The classified sentiment ('positive', 'negative', 'neutral').
                        - 'score': The sentiment score (polarity for TextBlob, compound for VADER, score for transformers).
                        - 'model': The model used for analysis.
                        - 'language': The language of the input text.

    Raises:
        ValueError: If the input text is empty or None.
        Exception: If an error occurs during sentiment analysis.
    """
    if not text or not isinstance(text, str):
        logger.error("Input text cannot be empty or None.")
        raise ValueError("Input text cannot be empty or None.")

    result: Dict[str, Any] = {
        "sentiment": "neutral",
        "score": 0.0,
        "model": model if model else "textblob",
        "language": language,
    }

    try:
        if model == "vader":
            logger.info("Using VADER sentiment analyzer.")
            nltk.download("vader_lexicon", quiet=True)
            sia = SentimentIntensityAnalyzer()
            scores = sia.polarity_scores(text)
            result["score"] = scores["compound"]
            if scores["compound"] >= 0.05:
                result["sentiment"] = "positive"
            elif scores["compound"] <= -0.05:
                result["sentiment"] = "negative"
            else:
                result["sentiment"] = "neutral"

        elif model == "transformers":
            logger.info("Using transformers sentiment pipeline.")
            sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model="distilbert-base-uncased-finetuned-sst-2-english",
            )
            analysis = sentiment_pipeline(text)[0]
            result["sentiment"] = analysis["label"].lower()
            result["score"] = analysis["score"]

        else:  # Default to TextBlob
            logger.info("Using TextBlob for sentiment analysis.")
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            result["score"] = polarity
            if polarity > 0:
                result["sentiment"] = "positive"
            elif polarity < 0:
                result["sentiment"] = "negative"
            else:
                result["sentiment"] = "neutral"

        logger.info(f"Sentiment analysis completed. Result: {result}")
        return result

    except Exception as e:
        logger.error(f"Error during sentiment analysis: {e}")
        raise Exception(f"Failed to analyze sentiment: {e}")
