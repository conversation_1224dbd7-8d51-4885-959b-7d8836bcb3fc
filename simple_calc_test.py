#!/usr/bin/env python3
"""简单的计算测试"""

import re
from src.self_expanding_agent.extensions.module_integrator import ModuleIntegrator

def test_direct_calculation():
    """直接测试计算功能"""
    print("🧮 直接测试计算功能")
    
    # 测试正则表达式提取
    task_description = "计算 15 + 27 的结果"
    arithmetic_pattern = r'(\d+(?:\.\d+)?)\s*([+\-*/])\s*(\d+(?:\.\d+)?)'
    match = re.search(arithmetic_pattern, task_description)
    
    if match:
        num1 = float(match.group(1))
        operator = match.group(2)
        num2 = float(match.group(3))
        
        print(f"✅ 成功提取: {num1} {operator} {num2}")
        
        # 直接计算
        if operator == "+":
            result = num1 + num2
        elif operator == "-":
            result = num1 - num2
        elif operator == "*":
            result = num1 * num2
        elif operator == "/":
            result = num1 / num2 if num2 != 0 else "Error: Division by zero"
        else:
            result = "Error: Unsupported operator"
        
        print(f"🎯 计算结果: {result}")
        
        # 测试使用生成的能力
        try:
            integrator = ModuleIntegrator()
            
            # 尝试调用生成的算术函数
            capability_result = integrator.call_capability_function(
                [num1, num2, operator],
                capability_name="basic_arithmetic_computation",
                function_name="compute_arithmetic"
            )
            print(f"🔧 使用能力计算结果: {capability_result}")
            
        except Exception as e:
            print(f"❌ 能力调用失败: {e}")
            
            # 尝试其他可能的能力名称
            try:
                capability_result = integrator.call_capability_function(
                    [num1, num2, operator],
                    capability_name="arithmetic_calculation",
                    function_name="perform_arithmetic_calculation"
                )
                print(f"🔧 使用备用能力计算结果: {capability_result}")
            except Exception as e2:
                print(f"❌ 备用能力调用也失败: {e2}")
    else:
        print("❌ 无法提取算术表达式")


def test_multiple_expressions():
    """测试多个表达式"""
    print("\n🧮 测试多个表达式")
    
    expressions = [
        "计算 15 + 27 的结果",
        "100 - 45",
        "8 * 9",
        "144 / 12",
        "求 25 + 75"
    ]
    
    arithmetic_pattern = r'(\d+(?:\.\d+)?)\s*([+\-*/])\s*(\d+(?:\.\d+)?)'
    
    for expr in expressions:
        print(f"\n📝 表达式: {expr}")
        match = re.search(arithmetic_pattern, expr)
        
        if match:
            num1 = float(match.group(1))
            operator = match.group(2)
            num2 = float(match.group(3))
            
            if operator == "+":
                result = num1 + num2
            elif operator == "-":
                result = num1 - num2
            elif operator == "*":
                result = num1 * num2
            elif operator == "/":
                result = num1 / num2 if num2 != 0 else "Error: Division by zero"
            else:
                result = "Error: Unsupported operator"
            
            print(f"   {num1} {operator} {num2} = {result}")
        else:
            print("   ❌ 无法解析")


if __name__ == "__main__":
    test_direct_calculation()
    test_multiple_expressions()
