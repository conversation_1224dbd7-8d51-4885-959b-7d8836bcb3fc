#!/usr/bin/env python3
"""测试算术计算功能"""

from src.self_expanding_agent.core.master_agent import MasterAgent

def test_calculation():
    """测试算术计算"""
    print("🧮 测试算术计算功能")
    
    agent = MasterAgent()
    
    # 测试不同的算术表达式
    test_cases = [
        "计算 15 + 27 的结果",
        "计算 100 - 45",
        "计算 8 * 9",
        "计算 144 / 12",
        "求 25 + 75 的和"
    ]
    
    for i, task in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {task}")
        
        try:
            result = agent.process_task(task)
            
            print(f"✅ 执行成功: {result['success']}")
            
            if result['success'] and 'result' in result:
                task_result = result['result']
                
                if isinstance(task_result, dict):
                    if 'result' in task_result:
                        print(f"🎯 计算结果: {task_result['result']}")
                    if 'expression' in task_result:
                        print(f"📝 表达式: {task_result['expression']}")
                    if 'capability_used' in task_result:
                        print(f"🔧 使用能力: {task_result['capability_used']}")
                else:
                    print(f"📊 结果: {task_result}")
            
            if result.get('new_capabilities_added'):
                print(f"🆕 新增能力: {', '.join(result['new_capabilities_added'])}")
                
        except Exception as e:
            print(f"❌ 执行失败: {e}")
        
        print("-" * 50)


if __name__ == "__main__":
    test_calculation()
