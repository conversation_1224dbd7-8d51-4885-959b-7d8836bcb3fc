import logging
from typing import List, Optional, Union

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def set_decimal_precision(
    number: Union[float, List[float]], precision: int, return_type: str = "float"
) -> Union[float, str, List[float], List[str]]:
    """
    Adjusts the decimal precision of a given floating-point number or a list of numbers.
    Rounds the number(s) to the specified number of decimal places.

    Args:
        number: The number or list of numbers to adjust the decimal precision for.
        precision: The number of decimal places to round the number(s) to.
        return_type: Specifies whether to return the result as a float or a string.
            Options: 'float' or 'str'. Default is 'float'.

    Returns:
        The number(s) rounded to the specified precision, in the specified return type.
        Returns float or str if input is a single number; returns List[float] or List[str] if input is a list.

    Raises:
        ValueError: If precision is negative or return_type is invalid.
        TypeError: If number is not a float or list of floats.
    """
    try:
        if not isinstance(precision, int) or precision < 0:
            raise ValueError("Precision must be a non-negative integer.")

        if return_type not in ("float", "str"):
            raise ValueError("return_type must be either 'float' or 'str'.")

        def process_single_num(num: float) -> Union[float, str]:
            rounded = round(num, precision)
            return str(rounded) if return_type == "str" else rounded

        if isinstance(number, list):
            logger.info(
                f"Processing list of numbers with precision {precision} and return_type {return_type}"
            )
            processed_numbers = [process_single_num(num) for num in number]
            return processed_numbers
        elif isinstance(number, (float, int)):
            logger.info(
                f"Processing single number with precision {precision} and return_type {return_type}"
            )
            return process_single_num(number)
        else:
            raise TypeError("Input must be a float or a list of floats.")

    except ValueError as ve:
        logger.error(f"ValueError in set_decimal_precision: {ve}")
        raise
    except TypeError as te:
        logger.error(f"TypeError in set_decimal_precision: {te}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in set_decimal_precision: {e}")
        raise
