"""Master agent that coordinates the self-expanding agent system."""

import json
import re
from typing import Any, Dict, List, Optional

from .ability_manager import Ability, AbilityManager
from .base_agent import BaseAgent
from .code_generator import CodeGenerator
from ..utils.logger import logger


class MasterAgent(BaseAgent):
    """Master agent that coordinates the entire self-expanding system."""

    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response, handling code blocks and other formatting."""
        try:
            # 首先尝试直接解析
            return json.loads(response)
        except json.JSONDecodeError:
            # 尝试提取JSON代码块
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1))
                except json.JSONDecodeError:
                    pass

            # 尝试提取第一个完整的JSON对象
            brace_match = re.search(r'\{.*\}', response, re.DOTALL)
            if brace_match:
                try:
                    return json.loads(brace_match.group(0))
                except json.JSONDecodeError:
                    pass

            # 如果都失败了，抛出原始错误
            raise json.JSONDecodeError(f"Could not parse JSON from response: {response[:200]}...", response, 0)

    def __init__(self):
        """Initialize the master agent."""
        super().__init__(
            name="MasterAgent",
            system_prompt=self._master_system_prompt(),
            capabilities=[
                "task_analysis",
                "capability_assessment", 
                "workflow_generation",
                "agent_coordination",
                "code_generation_management",
                "self_expansion"
            ]
        )
        
        self.ability_manager = AbilityManager()
        self.code_generator = CodeGenerator()
        self.active_agents: Dict[str, BaseAgent] = {}
        
        # 注册基础能力
        self._register_core_abilities()
        
        logger.info("MasterAgent initialized with core capabilities")
    
    def _master_system_prompt(self) -> str:
        """System prompt for the master agent."""
        return """You are the Master Agent of a self-expanding intelligent agent system.

Your primary responsibilities:
1. Analyze incoming tasks and break them down into manageable components
2. Assess whether current system capabilities are sufficient for the task
3. Identify missing capabilities and coordinate their development
4. Generate workflows and coordinate multiple agents
5. Manage the overall system state and ensure task completion

When you encounter a task that requires capabilities not currently available:
1. Clearly identify what new capability is needed
2. Determine if it requires new code, API integration, or both
3. Coordinate with the code generator to create the necessary components
4. Test and integrate the new capability
5. Update the system's capability registry

Always be systematic, thorough, and ensure that new capabilities are properly tested before integration.

You can request help from specialized components:
- CodeGenerator: For creating new Python functions and classes
- AbilityManager: For managing and tracking system capabilities
- WorkflowGenerator: For creating LangGraph workflows
- APIDiscovery: For finding and integrating external APIs

Be proactive in expanding the system's capabilities while maintaining stability and security."""
    
    def _register_core_abilities(self) -> None:
        """Register core system abilities."""
        core_abilities = [
            Ability(
                name="task_analysis",
                description="Analyze tasks and break them down into components",
                category="core",
                requirements=["natural_language_processing"]
            ),
            Ability(
                name="capability_assessment",
                description="Assess whether system has required capabilities",
                category="core",
                requirements=["system_introspection"]
            ),
            Ability(
                name="code_generation",
                description="Generate Python code for new capabilities",
                category="development",
                requirements=["python", "ast", "black", "isort"]
            ),
            Ability(
                name="workflow_coordination",
                description="Coordinate multiple agents in workflows",
                category="orchestration",
                requirements=["langgraph", "async_processing"]
            )
        ]
        
        for ability in core_abilities:
            self.ability_manager.register_ability(ability)
            self.ability_manager.assign_ability_to_agent(self.state.agent_id, ability.name)
    
    def process_task(self, task: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a task using the self-expanding system.
        
        Args:
            task: Task description
            context: Optional context information
            
        Returns:
            Task result with execution details
        """
        logger.info(f"MasterAgent processing task: {task}")
        self.update_status("thinking")
        
        try:
            # 1. 分析任务
            analysis = self._analyze_task(task, context)
            
            # 2. 评估能力需求
            capability_assessment = self._assess_capabilities(analysis)
            
            # 3. 如果需要新能力，则生成
            if capability_assessment["missing_capabilities"]:
                self._expand_capabilities(capability_assessment["missing_capabilities"])
            
            # 4. 执行任务
            self.update_status("working")
            result = self._execute_task(analysis, context)
            
            self.update_status("idle")
            logger.info(f"Task completed successfully: {task}")
            
            return {
                "success": True,
                "result": result,
                "analysis": analysis,
                "capability_assessment": capability_assessment,
                "new_capabilities_added": capability_assessment["missing_capabilities"]
            }
            
        except Exception as e:
            self.update_status("error")
            logger.error(f"Task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "task": task
            }
    
    def _analyze_task(self, task: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Analyze a task to understand requirements.
        
        Args:
            task: Task description
            context: Optional context
            
        Returns:
            Task analysis results
        """
        analysis_prompt = f"""Analyze the following task and provide a structured breakdown:

Task: {task}
Context: {context or 'None provided'}

Please provide:
1. Task type and category
2. Required capabilities/skills
3. Potential sub-tasks or steps
4. Expected inputs and outputs
5. Complexity level (1-10)
6. Estimated execution time
7. Dependencies or prerequisites
8. Success criteria

Format your response as a JSON object with these fields:
- task_type: string
- category: string  
- required_capabilities: list of strings
- sub_tasks: list of strings
- inputs: list of strings
- outputs: list of strings
- complexity: integer (1-10)
- estimated_time: string
- dependencies: list of strings
- success_criteria: list of strings

Respond only with the JSON object, no additional text."""
        
        try:
            response = self._call_llm([
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": analysis_prompt}
            ])
            
            # 尝试解析JSON响应
            analysis = self._parse_json_response(response)
            logger.info(f"Task analysis completed for: {task}")
            return analysis

        except json.JSONDecodeError:
            logger.warning("Failed to parse task analysis JSON, using fallback")
            return {
                "task_type": "general",
                "category": "unknown",
                "required_capabilities": ["general_processing"],
                "sub_tasks": [task],
                "inputs": ["user_input"],
                "outputs": ["result"],
                "complexity": 5,
                "estimated_time": "unknown",
                "dependencies": [],
                "success_criteria": ["task_completed"]
            }
    
    def _assess_capabilities(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Assess whether system has required capabilities.
        
        Args:
            analysis: Task analysis results
            
        Returns:
            Capability assessment
        """
        required_capabilities = analysis.get("required_capabilities", [])
        available_abilities = self.ability_manager.list_abilities()
        available_capability_names = [ability.name for ability in available_abilities]
        
        missing_capabilities = []
        for capability in required_capabilities:
            if capability not in available_capability_names:
                # 检查是否有相似的能力
                similar_abilities = self.ability_manager.search_abilities(capability)
                if not similar_abilities:
                    missing_capabilities.append(capability)
        
        assessment = {
            "required_capabilities": required_capabilities,
            "available_capabilities": available_capability_names,
            "missing_capabilities": missing_capabilities,
            "capability_gap": len(missing_capabilities) > 0,
            "readiness_score": max(0, 1 - len(missing_capabilities) / max(1, len(required_capabilities)))
        }
        
        logger.info(f"Capability assessment: {len(missing_capabilities)} missing capabilities")
        return assessment
    
    def _expand_capabilities(self, missing_capabilities: List[str]) -> None:
        """Expand system capabilities by generating new code.
        
        Args:
            missing_capabilities: List of missing capability names
        """
        logger.info(f"Expanding capabilities: {missing_capabilities}")
        
        for capability in missing_capabilities:
            try:
                # 生成能力实现
                self._generate_capability_implementation(capability)
                logger.info(f"Successfully generated capability: {capability}")
                
            except Exception as e:
                logger.error(f"Failed to generate capability {capability}: {e}")
    
    def _generate_capability_implementation(self, capability: str) -> None:
        """Generate implementation for a specific capability.
        
        Args:
            capability: Capability name to implement
        """
        # 分析能力需求
        capability_prompt = f"""Analyze the capability '{capability}' and provide implementation details:

1. What type of implementation is needed? (function, class, or module)
2. What are the key parameters/inputs?
3. What should it return/output?
4. What external dependencies might be needed?
5. What would be a good function/class name?

Provide a JSON response with:
- implementation_type: "function" or "class"
- name: suggested name for the implementation
- description: detailed description
- parameters: list of parameter objects with name, type, description
- return_type: return type
- dependencies: list of required packages
- category: capability category

Respond only with JSON."""
        
        try:
            response = self._call_llm([
                {"role": "system", "content": "You are a software architect designing capability implementations."},
                {"role": "user", "content": capability_prompt}
            ])
            
            spec = self._parse_json_response(response)
            
            # 生成代码
            if spec["implementation_type"] == "function":
                code = self.code_generator.generate_function_code(
                    function_name=spec["name"],
                    description=spec["description"],
                    parameters=spec["parameters"],
                    return_type=spec.get("return_type", "Any"),
                    requirements=spec.get("dependencies", [])
                )
            else:
                code = self.code_generator.generate_class_code(
                    class_name=spec["name"],
                    description=spec["description"],
                    methods=spec.get("methods", []),
                    requirements=spec.get("dependencies", [])
                )
            
            # 验证代码
            is_valid, error = self.code_generator.validate_code(code)
            if not is_valid:
                raise ValueError(f"Generated code is invalid: {error}")
            
            # 保存代码
            file_path = self.code_generator.save_code(
                code=code,
                filename=spec["name"].lower(),
                module_name="capabilities"
            )
            
            # 注册新能力
            new_ability = Ability(
                name=capability,
                description=spec["description"],
                category=spec.get("category", "generated"),
                code_path=str(file_path),
                requirements=spec.get("dependencies", [])
            )
            
            self.ability_manager.register_ability(new_ability)
            self.ability_manager.assign_ability_to_agent(self.state.agent_id, capability)
            
            logger.info(f"Generated and registered capability: {capability}")
            
        except Exception as e:
            logger.error(f"Failed to generate capability implementation for {capability}: {e}")
            raise
    
    def _execute_task(self, analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """Execute the analyzed task.
        
        Args:
            analysis: Task analysis results
            context: Optional context
            
        Returns:
            Task execution result
        """
        # 这里是一个简化的执行逻辑
        # 在实际实现中，这里会根据任务类型调用相应的处理逻辑
        
        task_type = analysis.get("task_type", "general")
        sub_tasks = analysis.get("sub_tasks", [])
        
        results = []
        for sub_task in sub_tasks:
            # 模拟子任务执行
            result = f"Executed: {sub_task}"
            results.append(result)
        
        return {
            "task_type": task_type,
            "sub_task_results": results,
            "status": "completed"
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status.
        
        Returns:
            System status information
        """
        abilities = self.ability_manager.list_abilities()
        
        return {
            "master_agent_status": self.state.status,
            "total_abilities": len(abilities),
            "active_agents": len(self.active_agents),
            "capabilities_by_category": self._group_abilities_by_category(abilities),
            "system_health": "healthy" if self.state.status != "error" else "degraded"
        }
    
    def _group_abilities_by_category(self, abilities: List[Ability]) -> Dict[str, int]:
        """Group abilities by category for status reporting.
        
        Args:
            abilities: List of abilities
            
        Returns:
            Dictionary of category counts
        """
        categories = {}
        for ability in abilities:
            category = ability.category
            categories[category] = categories.get(category, 0) + 1
        return categories
