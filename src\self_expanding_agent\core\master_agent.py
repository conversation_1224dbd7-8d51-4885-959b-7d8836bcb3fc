"""Master agent that coordinates the self-expanding agent system."""

import json
import re
from typing import Any, Dict, List, Optional

from .ability_manager import Ability, AbilityManager
from .base_agent import BaseAgent
from .code_generator import CodeGenerator
from ..utils.logger import logger


class MasterAgent(BaseAgent):
    """Master agent that coordinates the entire self-expanding system."""

    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response, handling code blocks and other formatting."""
        try:
            # 首先尝试直接解析
            return json.loads(response)
        except json.JSONDecodeError:
            # 尝试提取JSON代码块
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(1))
                except json.JSONDecodeError:
                    pass

            # 尝试提取第一个完整的JSON对象
            brace_match = re.search(r'\{.*\}', response, re.DOTALL)
            if brace_match:
                try:
                    return json.loads(brace_match.group(0))
                except json.JSONDecodeError:
                    pass

            # 如果都失败了，抛出原始错误
            raise json.JSONDecodeError(f"Could not parse JSON from response: {response[:200]}...", response, 0)

    def __init__(self):
        """Initialize the master agent."""
        super().__init__(
            name="MasterAgent",
            system_prompt=self._master_system_prompt(),
            capabilities=[
                "task_analysis",
                "capability_assessment", 
                "workflow_generation",
                "agent_coordination",
                "code_generation_management",
                "self_expansion"
            ]
        )
        
        self.ability_manager = AbilityManager()
        self.code_generator = CodeGenerator()
        self.active_agents: Dict[str, BaseAgent] = {}
        
        # 注册基础能力
        self._register_core_abilities()
        
        logger.info("MasterAgent initialized with core capabilities")
    
    def _master_system_prompt(self) -> str:
        """System prompt for the master agent."""
        return """You are the Master Agent of a self-expanding intelligent agent system.

Your primary responsibilities:
1. Analyze incoming tasks and break them down into manageable components
2. Assess whether current system capabilities are sufficient for the task
3. Identify missing capabilities and coordinate their development
4. Generate workflows and coordinate multiple agents
5. Manage the overall system state and ensure task completion

When you encounter a task that requires capabilities not currently available:
1. Clearly identify what new capability is needed
2. Determine if it requires new code, API integration, or both
3. Coordinate with the code generator to create the necessary components
4. Test and integrate the new capability
5. Update the system's capability registry

Always be systematic, thorough, and ensure that new capabilities are properly tested before integration.

You can request help from specialized components:
- CodeGenerator: For creating new Python functions and classes
- AbilityManager: For managing and tracking system capabilities
- WorkflowGenerator: For creating LangGraph workflows
- APIDiscovery: For finding and integrating external APIs

Be proactive in expanding the system's capabilities while maintaining stability and security."""
    
    def _register_core_abilities(self) -> None:
        """Register core system abilities."""
        core_abilities = [
            Ability(
                name="task_analysis",
                description="Analyze tasks and break them down into components",
                category="core",
                requirements=["natural_language_processing"]
            ),
            Ability(
                name="capability_assessment",
                description="Assess whether system has required capabilities",
                category="core",
                requirements=["system_introspection"]
            ),
            Ability(
                name="code_generation",
                description="Generate Python code for new capabilities",
                category="development",
                requirements=["python", "ast", "black", "isort"]
            ),
            Ability(
                name="workflow_coordination",
                description="Coordinate multiple agents in workflows",
                category="orchestration",
                requirements=["langgraph", "async_processing"]
            )
        ]
        
        for ability in core_abilities:
            self.ability_manager.register_ability(ability)
            self.ability_manager.assign_ability_to_agent(self.state.agent_id, ability.name)
    
    def process_task(self, task: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a task using the self-expanding system.
        
        Args:
            task: Task description
            context: Optional context information
            
        Returns:
            Task result with execution details
        """
        logger.info(f"MasterAgent processing task: {task}")
        self.update_status("thinking")
        
        try:
            # 1. 分析任务
            analysis = self._analyze_task(task, context)
            
            # 2. 评估能力需求
            capability_assessment = self._assess_capabilities(analysis)
            
            # 3. 如果需要新能力，则生成
            if capability_assessment["missing_capabilities"]:
                self._expand_capabilities(capability_assessment["missing_capabilities"])
            
            # 4. 执行任务
            self.update_status("working")
            result = self._execute_task(analysis, context)
            
            self.update_status("idle")
            logger.info(f"Task completed successfully: {task}")
            
            return {
                "success": True,
                "result": result,
                "analysis": analysis,
                "capability_assessment": capability_assessment,
                "new_capabilities_added": capability_assessment["missing_capabilities"]
            }
            
        except Exception as e:
            self.update_status("error")
            logger.error(f"Task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "task": task
            }
    
    def _analyze_task(self, task: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Analyze a task to understand requirements.
        
        Args:
            task: Task description
            context: Optional context
            
        Returns:
            Task analysis results
        """
        analysis_prompt = f"""Analyze the following task and provide a structured breakdown:

Task: {task}
Context: {context or 'None provided'}

Please provide:
1. Task type and category
2. Required capabilities/skills
3. Potential sub-tasks or steps
4. Expected inputs and outputs
5. Complexity level (1-10)
6. Estimated execution time
7. Dependencies or prerequisites
8. Success criteria

Format your response as a JSON object with these fields:
- task_type: string
- category: string  
- required_capabilities: list of strings
- sub_tasks: list of strings
- inputs: list of strings
- outputs: list of strings
- complexity: integer (1-10)
- estimated_time: string
- dependencies: list of strings
- success_criteria: list of strings

Respond only with the JSON object, no additional text."""
        
        try:
            response = self._call_llm([
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": analysis_prompt}
            ])
            
            # 尝试解析JSON响应
            analysis = self._parse_json_response(response)
            logger.info(f"Task analysis completed for: {task}")
            return analysis

        except json.JSONDecodeError:
            logger.warning("Failed to parse task analysis JSON, using fallback")
            return {
                "task_type": "general",
                "category": "unknown",
                "required_capabilities": ["general_processing"],
                "sub_tasks": [task],
                "inputs": ["user_input"],
                "outputs": ["result"],
                "complexity": 5,
                "estimated_time": "unknown",
                "dependencies": [],
                "success_criteria": ["task_completed"]
            }
    
    def _assess_capabilities(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Assess whether system has required capabilities.
        
        Args:
            analysis: Task analysis results
            
        Returns:
            Capability assessment
        """
        required_capabilities = analysis.get("required_capabilities", [])
        available_abilities = self.ability_manager.list_abilities()
        available_capability_names = [ability.name for ability in available_abilities]
        
        missing_capabilities = []
        for capability in required_capabilities:
            if capability not in available_capability_names:
                # 检查是否有相似的能力
                similar_abilities = self.ability_manager.search_abilities(capability)
                if not similar_abilities:
                    missing_capabilities.append(capability)
        
        assessment = {
            "required_capabilities": required_capabilities,
            "available_capabilities": available_capability_names,
            "missing_capabilities": missing_capabilities,
            "capability_gap": len(missing_capabilities) > 0,
            "readiness_score": max(0, 1 - len(missing_capabilities) / max(1, len(required_capabilities)))
        }
        
        logger.info(f"Capability assessment: {len(missing_capabilities)} missing capabilities")
        return assessment
    
    def _expand_capabilities(self, missing_capabilities: List[str]) -> None:
        """Expand system capabilities by generating new code.
        
        Args:
            missing_capabilities: List of missing capability names
        """
        logger.info(f"Expanding capabilities: {missing_capabilities}")
        
        for capability in missing_capabilities:
            try:
                # 生成能力实现
                self._generate_capability_implementation(capability)
                logger.info(f"Successfully generated capability: {capability}")
                
            except Exception as e:
                logger.error(f"Failed to generate capability {capability}: {e}")
    
    def _generate_capability_implementation(self, capability: str) -> None:
        """Generate implementation for a specific capability.
        
        Args:
            capability: Capability name to implement
        """
        # 分析能力需求
        capability_prompt = f"""Analyze the capability '{capability}' and provide implementation details:

1. What type of implementation is needed? (function, class, or module)
2. What are the key parameters/inputs?
3. What should it return/output?
4. What external dependencies might be needed?
5. What would be a good function/class name?

Provide a JSON response with:
- implementation_type: "function" or "class"
- name: suggested name for the implementation
- description: detailed description
- parameters: list of parameter objects with name, type, description
- return_type: return type
- dependencies: list of required packages
- category: capability category

Respond only with JSON."""
        
        try:
            response = self._call_llm([
                {"role": "system", "content": "You are a software architect designing capability implementations."},
                {"role": "user", "content": capability_prompt}
            ])
            
            spec = self._parse_json_response(response)
            
            # 生成代码
            if spec["implementation_type"] == "function":
                code = self.code_generator.generate_function_code(
                    function_name=spec["name"],
                    description=spec["description"],
                    parameters=spec["parameters"],
                    return_type=spec.get("return_type", "Any"),
                    requirements=spec.get("dependencies", [])
                )
            else:
                code = self.code_generator.generate_class_code(
                    class_name=spec["name"],
                    description=spec["description"],
                    methods=spec.get("methods", []),
                    requirements=spec.get("dependencies", [])
                )
            
            # 验证代码
            is_valid, error = self.code_generator.validate_code(code)
            if not is_valid:
                raise ValueError(f"Generated code is invalid: {error}")
            
            # 保存代码
            file_path = self.code_generator.save_code(
                code=code,
                filename=spec["name"].lower(),
                module_name="capabilities"
            )
            
            # 注册新能力
            new_ability = Ability(
                name=capability,
                description=spec["description"],
                category=spec.get("category", "generated"),
                code_path=str(file_path),
                requirements=spec.get("dependencies", [])
            )
            
            self.ability_manager.register_ability(new_ability)
            self.ability_manager.assign_ability_to_agent(self.state.agent_id, capability)
            
            logger.info(f"Generated and registered capability: {capability}")
            
        except Exception as e:
            logger.error(f"Failed to generate capability implementation for {capability}: {e}")
            raise
    
    def _execute_task(self, analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """Execute the analyzed task using pure capability-driven approach.

        This method implements a truly self-evolving execution system that:
        1. Dynamically discovers and matches capabilities
        2. Uses LLM to generate execution strategies
        3. Delegates all actual work to registered capabilities
        4. Never contains hardcoded business logic

        Args:
            analysis: Task analysis results
            context: Optional context

        Returns:
            Task execution result
        """
        logger.info("Starting capability-driven task execution")

        try:
            # 1. 智能能力发现和匹配
            capability_matcher = self._create_capability_matcher()
            matched_capabilities = capability_matcher.find_suitable_capabilities(analysis)

            if not matched_capabilities:
                logger.warning("No suitable capabilities found for task")
                return {
                    "status": "failed",
                    "error": "No suitable capabilities available",
                    "suggestion": "Consider generating new capabilities for this task type"
                }

            # 2. LLM生成执行策略
            execution_strategy = self._generate_execution_strategy(analysis, matched_capabilities)
            logger.info(f"Generated execution strategy: {execution_strategy}")

            # 3. 执行策略
            executor = self._create_capability_executor()
            result = executor.execute_strategy(execution_strategy, analysis, context)

            return {
                "task_type": analysis.get("task_type", "unknown"),
                "matched_capabilities": [cap["name"] for cap in matched_capabilities],
                "execution_strategy": execution_strategy,
                "result": result,
                "status": "completed",
                "method": "capability_driven_execution"
            }

        except Exception as e:
            logger.error(f"Capability-driven execution failed: {e}")
            return {
                "status": "failed",
                "error": f"Execution failed: {str(e)}",
                "analysis": analysis
            }

    def _create_capability_matcher(self):
        """创建能力匹配器"""
        from src.self_expanding_agent.core.capability_matcher import CapabilityMatcher
        return CapabilityMatcher(self.ability_manager)

    def _create_capability_executor(self):
        """创建能力执行器"""
        from src.self_expanding_agent.core.capability_executor import CapabilityExecutor
        return CapabilityExecutor()

    def _generate_execution_strategy(self, analysis: Dict[str, Any], matched_capabilities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用LLM生成执行策略

        Args:
            analysis: 任务分析结果
            matched_capabilities: 匹配的能力列表

        Returns:
            执行策略
        """
        prompt = f"""
基于以下任务分析和匹配的能力，生成一个智能执行策略。

任务分析:
{json.dumps(analysis, ensure_ascii=False, indent=2)}

匹配的能力:
{json.dumps(matched_capabilities, ensure_ascii=False, indent=2)}

请生成一个JSON格式的执行策略，包含以下字段:
- steps: 执行步骤列表，每个步骤包含:
  - action: 动作类型 ("call_capability", "smart_invoke", "direct_computation", "data_processing")
  - capability_name: 要使用的能力名称
  - code_path: 能力代码路径（如果使用smart_invoke）
  - function_name: 函数名称（如果已知）
  - parameters: 参数列表（从任务分析中提取的实际值）
  - critical: 是否为关键步骤（失败时是否停止执行）
- execution_order: 步骤执行顺序
- fallback_strategy: 备选策略

优先使用smart_invoke动作，它能自动发现和调用最合适的函数。
参数应该是从任务分析中提取的实际值，不是描述性文本。

返回纯JSON格式，不要包含任何其他文本。
"""

        try:
            response = self._call_llm([{"role": "user", "content": prompt}])
            strategy = self._parse_json_response(response)

            # 为smart_invoke步骤添加code_path
            for step in strategy.get("steps", []):
                if step.get("action") == "smart_invoke":
                    capability_name = step.get("capability_name", "")
                    # 从匹配的能力中找到对应的code_path
                    for cap in matched_capabilities:
                        if cap["name"] == capability_name:
                            step["code_path"] = cap["code_path"]
                            break

            return strategy

        except Exception as e:
            logger.error(f"Failed to generate execution strategy: {e}")
            # 返回简单的默认策略
            if matched_capabilities:
                best_capability = matched_capabilities[0]
                return {
                    "steps": [
                        {
                            "action": "smart_invoke",
                            "capability_name": best_capability["name"],
                            "code_path": best_capability["code_path"],
                            "parameters": analysis.get("inputs", []),
                            "critical": True
                        }
                    ],
                    "execution_order": [0],
                    "fallback_strategy": {
                        "steps": [
                            {
                                "action": "direct_computation",
                                "parameters": analysis.get("inputs", [])
                            }
                        ]
                    }
                }
            else:
                return {
                    "steps": [
                        {
                            "action": "direct_computation",
                            "parameters": analysis.get("inputs", [])
                        }
                    ],
                    "execution_order": [0],
                    "fallback_strategy": {}
                }

    def _generate_execution_plan(self, analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate an execution plan using LLM.

        Args:
            analysis: Task analysis results
            context: Optional context

        Returns:
            Execution plan with capability calls and parameters
        """
        # 获取可用能力列表
        available_abilities = self.ability_manager.list_abilities()
        ability_descriptions = []

        for ability in available_abilities:
            if ability.code_path:  # 只包含有实际代码的能力
                ability_descriptions.append({
                    "name": ability.name,
                    "description": ability.description,
                    "category": ability.category
                })

        prompt = f"""
基于以下任务分析和可用能力，生成一个详细的执行计划。

任务分析:
{json.dumps(analysis, ensure_ascii=False, indent=2)}

可用能力:
{json.dumps(ability_descriptions, ensure_ascii=False, indent=2)}

请生成一个JSON格式的执行计划，包含以下字段:
- steps: 执行步骤列表，每个步骤包含:
  - action: 要执行的动作类型 ("call_capability", "direct_computation", "data_processing")
  - capability_name: 要调用的能力名称（如果action是call_capability）
  - function_name: 要调用的函数名称
  - parameters: 函数参数列表
  - expected_output: 预期输出类型
- execution_order: 步骤执行顺序
- fallback_strategy: 如果主要计划失败的备选方案

返回纯JSON格式，不要包含任何其他文本。
"""

        try:
            response = self._call_llm([{"role": "user", "content": prompt}])
            execution_plan = self._parse_json_response(response)
            return execution_plan
        except Exception as e:
            logger.error(f"Failed to generate execution plan: {e}")
            # 返回简单的默认计划
            return {
                "steps": [
                    {
                        "action": "direct_computation",
                        "description": "Execute task using available capabilities",
                        "parameters": analysis.get("inputs", []),
                        "expected_output": "task_result"
                    }
                ],
                "execution_order": [0],
                "fallback_strategy": "use_simple_execution"
            }

    def _execute_with_capabilities(self, execution_plan: Dict[str, Any], analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """Execute task using the generated execution plan and available capabilities.

        Args:
            execution_plan: Generated execution plan
            analysis: Task analysis results
            context: Optional context

        Returns:
            Execution result
        """
        from src.self_expanding_agent.extensions.module_integrator import ModuleIntegrator

        integrator = ModuleIntegrator()
        steps = execution_plan.get("steps", [])
        execution_order = execution_plan.get("execution_order", list(range(len(steps))))

        results = []

        for step_index in execution_order:
            if step_index >= len(steps):
                continue

            step = steps[step_index]
            action = step.get("action", "")

            try:
                if action == "call_capability":
                    # 调用特定能力
                    capability_name = step.get("capability_name", "")
                    function_name = step.get("function_name", "")
                    parameters = step.get("parameters", [])

                    logger.info(f"Calling capability: {capability_name}.{function_name} with params: {parameters}")

                    result = integrator.call_capability_function(
                        parameters,
                        capability_name=capability_name.replace(" ", "_"),
                        function_name=function_name
                    )

                    results.append({
                        "step": step_index,
                        "action": action,
                        "capability": capability_name,
                        "function": function_name,
                        "result": result,
                        "status": "success"
                    })

                elif action == "direct_computation":
                    # 直接计算（用于简单数学运算等）
                    result = self._perform_direct_computation(step, analysis)
                    results.append({
                        "step": step_index,
                        "action": action,
                        "result": result,
                        "status": "success"
                    })

                else:
                    logger.warning(f"Unknown action type: {action}")
                    results.append({
                        "step": step_index,
                        "action": action,
                        "status": "skipped",
                        "reason": "unknown_action_type"
                    })

            except Exception as e:
                logger.error(f"Step {step_index} failed: {e}")
                results.append({
                    "step": step_index,
                    "action": action,
                    "status": "failed",
                    "error": str(e)
                })

        # 返回最终结果
        successful_results = [r for r in results if r.get("status") == "success"]
        if successful_results:
            # 如果有成功的步骤，返回最后一个成功结果
            final_result = successful_results[-1].get("result", "No result")
            return {
                "final_result": final_result,
                "execution_steps": results,
                "success_count": len(successful_results),
                "total_steps": len(steps)
            }
        else:
            return {
                "final_result": "No successful execution",
                "execution_steps": results,
                "success_count": 0,
                "total_steps": len(steps)
            }

    def _perform_direct_computation(self, step: Dict[str, Any], analysis: Dict[str, Any]) -> Any:
        """Perform direct computation for simple tasks.

        Args:
            step: Execution step
            analysis: Task analysis

        Returns:
            Computation result
        """
        # 尝试从分析中提取数学表达式
        inputs = analysis.get("inputs", [])
        task_type = analysis.get("task_type", "")

        if "arithmetic" in task_type.lower() and len(inputs) >= 2:
            try:
                num1 = float(inputs[0])
                num2 = float(inputs[1])

                # 从子任务中推断操作
                sub_tasks = analysis.get("sub_tasks", [])
                operator = "+"  # 默认

                for task in sub_tasks:
                    if "add" in task.lower() or "sum" in task.lower():
                        operator = "+"
                    elif "subtract" in task.lower() or "minus" in task.lower():
                        operator = "-"
                    elif "multiply" in task.lower() or "times" in task.lower():
                        operator = "*"
                    elif "divide" in task.lower():
                        operator = "/"

                # 执行计算
                if operator == "+":
                    result = num1 + num2
                elif operator == "-":
                    result = num1 - num2
                elif operator == "*":
                    result = num1 * num2
                elif operator == "/":
                    result = num1 / num2 if num2 != 0 else "Error: Division by zero"
                else:
                    result = "Error: Unknown operator"

                logger.info(f"Direct computation: {num1} {operator} {num2} = {result}")
                return result

            except (ValueError, IndexError) as e:
                logger.error(f"Direct computation failed: {e}")
                return f"Computation error: {str(e)}"

        return "Direct computation not applicable"

    def _fallback_execution(self, analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """Fallback execution when dynamic execution fails.

        Args:
            analysis: Task analysis results
            context: Optional context

        Returns:
            Fallback execution result
        """
        # 简单的基于规则的回退执行
        task_type = analysis.get("task_type", "general")
        sub_tasks = analysis.get("sub_tasks", [])

        if "arithmetic" in task_type.lower():
            # 尝试直接计算
            return self._perform_direct_computation({}, analysis)
        else:
            # 通用回退
            results = []
            for sub_task in sub_tasks:
                results.append(f"Executed: {sub_task}")

            return {
                "sub_task_results": results,
                "method": "simple_fallback"
            }

    def _execute_arithmetic_task(self, analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """Execute arithmetic calculation tasks."""
        import re
        from src.self_expanding_agent.extensions.module_integrator import ModuleIntegrator

        # 从任务描述中提取数字和操作符
        task_description = analysis.get("task_description", "")

        # 使用正则表达式提取算术表达式
        # 匹配类似 "15 + 27", "计算 15 + 27", "15+27" 等格式
        arithmetic_pattern = r'(\d+(?:\.\d+)?)\s*([+\-*/])\s*(\d+(?:\.\d+)?)'
        match = re.search(arithmetic_pattern, task_description)

        if match:
            num1 = float(match.group(1))
            operator = match.group(2)
            num2 = float(match.group(3))

            logger.info(f"Extracted arithmetic: {num1} {operator} {num2}")

            # 查找算术计算能力
            abilities = self.ability_manager.list_abilities()
            arithmetic_ability = None

            for ability in abilities:
                if "arithmetic" in ability.name.lower() or "calculation" in ability.name.lower():
                    arithmetic_ability = ability
                    break

            if arithmetic_ability and arithmetic_ability.code_path:
                try:
                    # 使用模块集成器执行计算
                    integrator = ModuleIntegrator()

                    # 尝试不同的函数名
                    function_names = ["compute_arithmetic", "calculate", "perform_calculation", "compute_basic_arithmetic"]

                    for func_name in function_names:
                        try:
                            if func_name == "compute_arithmetic":
                                result = integrator.call_capability_function(
                                    [num1, num2, operator],
                                    capability_name=arithmetic_ability.name.replace(" ", "_"),
                                    function_name=func_name
                                )
                            else:
                                result = integrator.call_capability_function(
                                    [num1, num2, operator],
                                    capability_name=arithmetic_ability.name.replace(" ", "_"),
                                    function_name=func_name
                                )

                            logger.info(f"Arithmetic calculation result: {result}")
                            return {
                                "task_type": "arithmetic_calculation",
                                "expression": f"{num1} {operator} {num2}",
                                "result": result,
                                "status": "completed",
                                "capability_used": arithmetic_ability.name
                            }
                        except Exception as e:
                            logger.debug(f"Function {func_name} not found or failed: {e}")
                            continue

                    # 如果所有函数名都失败，尝试直接计算
                    if operator == "+":
                        result = num1 + num2
                    elif operator == "-":
                        result = num1 - num2
                    elif operator == "*":
                        result = num1 * num2
                    elif operator == "/":
                        result = num1 / num2 if num2 != 0 else "Error: Division by zero"
                    else:
                        result = "Error: Unsupported operator"

                    return {
                        "task_type": "arithmetic_calculation",
                        "expression": f"{num1} {operator} {num2}",
                        "result": result,
                        "status": "completed",
                        "method": "direct_calculation"
                    }

                except Exception as e:
                    logger.error(f"Failed to execute arithmetic capability: {e}")

        return {
            "task_type": "arithmetic_calculation",
            "status": "failed",
            "error": "Could not parse arithmetic expression or execute calculation"
        }

    def _execute_text_task(self, analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """Execute text processing tasks."""
        # 简化的文本处理执行
        return {
            "task_type": "text_processing",
            "status": "completed",
            "note": "Text processing capabilities available but not fully implemented in this demo"
        }

    def _execute_general_task(self, analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Any:
        """Execute general tasks."""
        sub_tasks = analysis.get("sub_tasks", [])
        results = []

        for sub_task in sub_tasks:
            result = f"Executed: {sub_task}"
            results.append(result)

        return {
            "task_type": analysis.get("task_type", "general"),
            "sub_task_results": results,
            "status": "completed"
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status.
        
        Returns:
            System status information
        """
        abilities = self.ability_manager.list_abilities()
        
        return {
            "master_agent_status": self.state.status,
            "total_abilities": len(abilities),
            "active_agents": len(self.active_agents),
            "capabilities_by_category": self._group_abilities_by_category(abilities),
            "system_health": "healthy" if self.state.status != "error" else "degraded"
        }
    
    def _group_abilities_by_category(self, abilities: List[Ability]) -> Dict[str, int]:
        """Group abilities by category for status reporting.
        
        Args:
            abilities: List of abilities
            
        Returns:
            Dictionary of category counts
        """
        categories = {}
        for ability in abilities:
            category = ability.category
            categories[category] = categories.get(category, 0) + 1
        return categories
