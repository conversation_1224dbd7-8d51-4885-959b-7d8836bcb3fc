"""Module integration system for dynamically loading and integrating generated code."""

import importlib
import importlib.util
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Union

from ..core.ability_manager import Ability, AbilityManager
from ..extensions.api_discovery import APIDiscovery, APIService
from ..extensions.dependency_manager import DependencyManager
from ..utils.logger import logger


class ModuleIntegrator:
    """Integrates dynamically generated modules into the system."""
    
    def __init__(self, ability_manager: Optional[AbilityManager] = None):
        """Initialize the module integrator.
        
        Args:
            ability_manager: Optional ability manager instance
        """
        self.ability_manager = ability_manager or AbilityManager()
        self.dependency_manager = DependencyManager()
        self.api_discovery = APIDiscovery()
        
        self.loaded_modules: Dict[str, Any] = {}
        self.integration_history: List[Dict[str, Any]] = []
        
        logger.info("Initialized ModuleIntegrator")
    
    def integrate_generated_code(
        self,
        code: str,
        module_name: str,
        capability_name: str,
        description: str,
        auto_install_deps: bool = True
    ) -> bool:
        """Integrate generated code as a new capability.
        
        Args:
            code: Generated Python code
            module_name: Name for the module
            capability_name: Name of the capability
            description: Description of the capability
            auto_install_deps: Whether to automatically install dependencies
            
        Returns:
            True if integration was successful
        """
        logger.info(f"Integrating code module: {module_name}")
        
        try:
            # 1. 分析和安装依赖
            if auto_install_deps:
                deps_success, installed_deps = self.dependency_manager.auto_resolve_dependencies(code)
                if not deps_success:
                    logger.warning("Some dependencies could not be installed, proceeding anyway")
            
            # 2. 保存代码到文件
            module_path = self._save_code_to_file(code, module_name)
            
            # 3. 动态加载模块
            loaded_module = self._load_module_from_file(module_path, module_name)
            if not loaded_module:
                return False
            
            # 4. 验证模块功能
            validation_result = self._validate_module(loaded_module, capability_name)
            if not validation_result["valid"]:
                logger.error(f"Module validation failed: {validation_result['error']}")
                return False
            
            # 5. 注册能力
            self._register_capability(
                capability_name=capability_name,
                description=description,
                module_path=module_path,
                module_object=loaded_module,
                functions=validation_result["functions"],
                classes=validation_result["classes"]
            )
            
            # 6. 记录集成历史
            self._record_integration(
                module_name=module_name,
                capability_name=capability_name,
                module_path=module_path,
                dependencies=installed_deps if auto_install_deps else []
            )
            
            logger.info(f"Successfully integrated module: {module_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to integrate module {module_name}: {e}")
            return False
    
    def integrate_api_capability(
        self,
        api_service: APIService,
        endpoint_name: str,
        capability_name: str
    ) -> bool:
        """Integrate an API as a new capability.
        
        Args:
            api_service: API service to integrate
            endpoint_name: Specific endpoint to integrate
            capability_name: Name for the new capability
            
        Returns:
            True if integration was successful
        """
        logger.info(f"Integrating API capability: {capability_name}")
        
        try:
            # 1. 生成API集成代码
            integration_code = self.api_discovery.generate_api_integration_code(
                api_service, endpoint_name
            )
            
            # 2. 测试API端点
            test_success, test_response = self.api_discovery.test_api_endpoint(
                api_service, endpoint_name
            )
            
            if not test_success:
                logger.warning(f"API test failed, but proceeding with integration: {test_response}")
            
            # 3. 集成代码
            module_name = f"api_{api_service.name.lower()}_{endpoint_name}"
            success = self.integrate_generated_code(
                code=integration_code,
                module_name=module_name,
                capability_name=capability_name,
                description=f"API integration for {api_service.name}.{endpoint_name}"
            )
            
            if success:
                # 4. 更新能力信息，添加API相关元数据
                ability = self.ability_manager.get_ability(capability_name)
                if ability:
                    ability.metadata = {
                        "api_service": api_service.name,
                        "endpoint": endpoint_name,
                        "base_url": api_service.base_url,
                        "requires_api_key": api_service.api_key_required
                    }
                    self.ability_manager.update_ability(ability)
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to integrate API capability {capability_name}: {e}")
            return False
    
    def _save_code_to_file(self, code: str, module_name: str) -> Path:
        """Save code to a Python file.
        
        Args:
            code: Python code
            module_name: Module name
            
        Returns:
            Path to the saved file
        """
        # 创建模块目录
        modules_dir = Path("data/generated_modules")
        modules_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存代码
        module_file = modules_dir / f"{module_name}.py"
        with open(module_file, 'w', encoding='utf-8') as f:
            f.write(code)
        
        logger.debug(f"Saved code to: {module_file}")
        return module_file
    
    def _load_module_from_file(self, module_path: Path, module_name: str) -> Optional[Any]:
        """Dynamically load a module from file.
        
        Args:
            module_path: Path to the module file
            module_name: Module name
            
        Returns:
            Loaded module object or None if failed
        """
        try:
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            if spec is None or spec.loader is None:
                logger.error(f"Failed to create module spec for: {module_path}")
                return None
            
            module = importlib.util.module_from_spec(spec)
            
            # 添加到sys.modules以便其他模块可以导入
            sys.modules[module_name] = module
            
            # 执行模块
            spec.loader.exec_module(module)
            
            # 缓存模块
            self.loaded_modules[module_name] = module
            
            logger.debug(f"Successfully loaded module: {module_name}")
            return module
            
        except Exception as e:
            logger.error(f"Failed to load module from {module_path}: {e}")
            return None
    
    def _validate_module(self, module: Any, capability_name: str) -> Dict[str, Any]:
        """Validate a loaded module.
        
        Args:
            module: Loaded module object
            capability_name: Expected capability name
            
        Returns:
            Validation results
        """
        try:
            # 获取模块中的函数和类
            functions = []
            classes = []
            
            for attr_name in dir(module):
                if not attr_name.startswith('_'):
                    attr = getattr(module, attr_name)

                    if isinstance(attr, type):
                        # 类
                        classes.append(attr_name)
                    elif callable(attr) and not hasattr(attr, '__self__'):
                        # 函数（排除方法）
                        functions.append(attr_name)
            
            # 基本验证：至少要有一个函数或类
            if not functions and not classes:
                return {
                    "valid": False,
                    "error": "Module contains no callable functions or classes",
                    "functions": [],
                    "classes": []
                }
            
            # 检查是否有与能力名称匹配的函数或类
            capability_found = (
                capability_name in functions or
                capability_name in classes or
                any(capability_name.lower() in func.lower() for func in functions) or
                any(capability_name.lower() in cls.lower() for cls in classes)
            )
            
            if not capability_found:
                logger.warning(f"No function/class matching capability '{capability_name}' found")
            
            return {
                "valid": True,
                "functions": functions,
                "classes": classes,
                "capability_found": capability_found
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": str(e),
                "functions": [],
                "classes": []
            }
    
    def _register_capability(
        self,
        capability_name: str,
        description: str,
        module_path: Path,
        module_object: Any,
        functions: List[str],
        classes: List[str]
    ) -> None:
        """Register a new capability in the ability manager.
        
        Args:
            capability_name: Name of the capability
            description: Description
            module_path: Path to the module file
            module_object: Loaded module object
            functions: List of function names in the module
            classes: List of class names in the module
        """
        ability = Ability(
            name=capability_name,
            description=description,
            category="generated",
            code_path=str(module_path),
            metadata={
                "module_name": module_object.__name__,
                "functions": functions,
                "classes": classes,
                "integration_type": "dynamic_code"
            }
        )
        
        self.ability_manager.register_ability(ability)
        logger.info(f"Registered capability: {capability_name}")
    
    def _record_integration(
        self,
        module_name: str,
        capability_name: str,
        module_path: Path,
        dependencies: List[str]
    ) -> None:
        """Record integration in history.
        
        Args:
            module_name: Module name
            capability_name: Capability name
            module_path: Path to module file
            dependencies: List of installed dependencies
        """
        from datetime import datetime
        
        record = {
            "timestamp": datetime.now().isoformat(),
            "module_name": module_name,
            "capability_name": capability_name,
            "module_path": str(module_path),
            "dependencies": dependencies,
            "status": "success"
        }
        
        self.integration_history.append(record)
    
    def get_loaded_module(self, module_name: str) -> Optional[Any]:
        """Get a loaded module by name.
        
        Args:
            module_name: Module name
            
        Returns:
            Module object or None if not found
        """
        return self.loaded_modules.get(module_name)
    
    def call_capability_function(
        self,
        capability_name: str,
        function_name: str,
        *args,
        **kwargs
    ) -> Any:
        """Call a function from an integrated capability.
        
        Args:
            capability_name: Name of the capability
            function_name: Name of the function to call
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            Function result
        """
        # 查找能力对应的模块
        ability = self.ability_manager.get_ability(capability_name)
        if not ability or not ability.metadata:
            raise ValueError(f"Capability {capability_name} not found")
        
        module_name = ability.metadata.get("module_name")
        if not module_name:
            raise ValueError(f"No module associated with capability {capability_name}")
        
        module = self.loaded_modules.get(module_name)
        if not module:
            raise ValueError(f"Module {module_name} not loaded")
        
        # 获取函数
        if not hasattr(module, function_name):
            raise ValueError(f"Function {function_name} not found in module {module_name}")
        
        func = getattr(module, function_name)
        if not callable(func):
            raise ValueError(f"{function_name} is not callable")
        
        # 调用函数
        try:
            result = func(*args, **kwargs)
            logger.info(f"Successfully called {capability_name}.{function_name}")
            return result
        except Exception as e:
            logger.error(f"Error calling {capability_name}.{function_name}: {e}")
            raise
    
    def unload_module(self, module_name: str) -> bool:
        """Unload a dynamically loaded module.
        
        Args:
            module_name: Module name to unload
            
        Returns:
            True if successfully unloaded
        """
        try:
            # 从缓存中移除
            if module_name in self.loaded_modules:
                del self.loaded_modules[module_name]
            
            # 从sys.modules中移除
            if module_name in sys.modules:
                del sys.modules[module_name]
            
            logger.info(f"Unloaded module: {module_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload module {module_name}: {e}")
            return False
    
    def list_integrated_capabilities(self) -> List[Dict[str, Any]]:
        """List all integrated capabilities.
        
        Returns:
            List of capability information
        """
        capabilities = []
        
        for ability in self.ability_manager.list_abilities():
            if ability.category == "generated":
                capabilities.append({
                    "name": ability.name,
                    "description": ability.description,
                    "module_path": ability.code_path,
                    "metadata": ability.metadata
                })
        
        return capabilities
