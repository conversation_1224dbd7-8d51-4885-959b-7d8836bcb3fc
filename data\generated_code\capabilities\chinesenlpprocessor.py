import logging
import os
from typing import Any, Dict, List, Optional

import jieba
import pyltp
import snownlp
import thulac


class ChineseNLPProcessor:
    """
    A class designed to handle various Chinese natural language processing tasks such as tokenization,
    part-of-speech tagging, named entity recognition, and sentiment analysis. It encapsulates common
    NLP operations for Chinese text processing, providing a unified interface for multiple NLP tasks.
    """

    def __init__(
        self,
        ltp_model_dir: Optional[str] = None,
        thulac_model_path: Optional[str] = None,
    ) -> None:
        """
        Initialize the ChineseNLPProcessor with optional model paths for LTP and THULAC.

        Args:
            ltp_model_dir (Optional[str]): Path to the LTP model directory. If None, LTP functionalities will be disabled.
            thulac_model_path (Optional[str]): Path to the THULAC model file. If None, THULAC will use the default model.
        """
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

        self.ltp_model_dir = ltp_model_dir
        self.thulac_model_path = thulac_model_path

        # Initialize Jieba (no external model needed)
        jieba.initialize()
        self.logger.info("Jieba initialized successfully.")

        # Initialize THULAC
        try:
            self.thulac_processor = thulac.thulac(model_path=thulac_model_path)
            self.logger.info("THULAC initialized successfully.")
        except Exception as e:
            self.logger.error(f"Failed to initialize THULAC: {e}")
            self.thulac_processor = None

        # LTP models will be loaded on-demand to save memory

    def tokenize(self, text: str, engine: str = "jieba") -> List[str]:
        """
        Tokenize Chinese text into words.

        Args:
            text (str): The input Chinese text to tokenize.
            engine (str): The tokenization engine to use ("jieba", "ltp", or "thulac"). Defaults to "jieba".

        Returns:
            List[str]: A list of tokens.

        Raises:
            ValueError: If the specified engine is not supported.
        """
        if not text:
            return []

        try:
            if engine == "jieba":
                tokens = jieba.lcut(text)
            elif engine == "ltp":
                if not self.ltp_model_dir:
                    raise ValueError(
                        "LTP model directory not provided during initialization."
                    )
                segmentor = pyltp.Segmentor()
                segmentor.load(os.path.join(self.ltp_model_dir, "cws.model"))
                tokens = segmentor.segment(text)
                segmentor.release()
            elif engine == "thulac":
                if not self.thulac_processor:
                    raise ValueError("THULAC not initialized.")
                result = self.thulac_processor.cut(text)
                tokens = [word for word, _ in result]
            else:
                raise ValueError(f"Unsupported tokenization engine: {engine}")

            self.logger.info(f"Tokenized text using {engine}.")
            return list(tokens)
        except Exception as e:
            self.logger.error(f"Tokenization failed: {e}")
            raise

    def pos_tag(self, text: str, engine: str = "thulac") -> List[tuple]:
        """
        Perform part-of-speech tagging on Chinese text.

        Args:
            text (str): The input Chinese text.
            engine (str): The POS tagging engine to use ("ltp" or "thulac"). Defaults to "thulac".

        Returns:
            List[tuple]: A list of (word, pos_tag) tuples.

        Raises:
            ValueError: If the specified engine is not supported.
        """
        if not text:
            return []

        try:
            if engine == "thulac":
                if not self.thulac_processor:
                    raise ValueError("THULAC not initialized.")
                result = self.thulac_processor.cut(text)
                return [(word, tag) for word, tag in result]
            elif engine == "ltp":
                if not self.ltp_model_dir:
                    raise ValueError(
                        "LTP model directory not provided during initialization."
                    )
                segmentor = pyltp.Segmentor()
                segmentor.load(os.path.join(self.ltp_model_dir, "cws.model"))
                words = segmentor.segment(text)
                segmentor.release()

                postagger = pyltp.Postagger()
                postagger.load(os.path.join(self.ltp_model_dir, "pos.model"))
                tags = postagger.postag(words)
                postagger.release()

                return list(zip(words, tags))
            else:
                raise ValueError(f"Unsupported POS tagging engine: {engine}")
        except Exception as e:
            self.logger.error(f"POS tagging failed: {e}")
            raise

    def ner(self, text: str) -> List[tuple]:
        """
        Perform named entity recognition on Chinese text using LTP.

        Args:
            text (str): The input Chinese text.

        Returns:
            List[tuple]: A list of (word, entity_type) tuples.

        Raises:
            ValueError: If LTP model directory is not provided.
        """
        if not text:
            return []

        if not self.ltp_model_dir:
            raise ValueError("LTP model directory not provided during initialization.")

        try:
            segmentor = pyltp.Segmentor()
            segmentor.load(os.path.join(self.ltp_model_dir, "cws.model"))
            words = segmentor.segment(text)
            segmentor.release()

            postagger = pyltp.Postagger()
            postagger.load(os.path.join(self.ltp_model_dir, "pos.model"))
            tags = postagger.postag(words)
            postagger.release()

            recognizer = pyltp.NamedEntityRecognizer()
            recognizer.load(os.path.join(self.ltp_model_dir, "ner.model"))
            netags = recognizer.recognize(words, tags)
            recognizer.release()

            return list(zip(words, netags))
        except Exception as e:
            self.logger.error(f"Named entity recognition failed: {e}")
            raise

    def sentiment_analysis(self, text: str) -> float:
        """
        Perform sentiment analysis on Chinese text using SnowNLP.

        Args:
            text (str): The input Chinese text.

        Returns:
            float: A sentiment score between 0 (negative) and 1 (positive).
        """
        if not text:
            return 0.5  # Neutral sentiment for empty text

        try:
            s = snownlp.SnowNLP(text)
            score = s.sentiments
            self.logger.info(f"Sentiment analysis completed with score: {score}")
            return score
        except Exception as e:
            self.logger.error(f"Sentiment analysis failed: {e}")
            return 0.5  # Fallback to neutral sentiment
