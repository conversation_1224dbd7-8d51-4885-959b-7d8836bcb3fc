#!/usr/bin/env python3
"""调试计算功能"""

from src.self_expanding_agent.core.master_agent import MasterAgent

def debug_calculation():
    """调试计算过程"""
    print("🔍 调试计算过程")
    
    agent = MasterAgent()
    task = "计算 15 + 27 的结果"
    
    print(f"📋 任务: {task}")
    
    try:
        # 手动执行各个步骤
        print("\n1️⃣ 分析任务...")
        analysis = agent._analyze_task(task)
        print(f"📊 分析结果: {analysis}")
        
        print("\n2️⃣ 评估能力...")
        capability_assessment = agent._assess_capabilities(analysis)
        print(f"📊 能力评估: {capability_assessment}")
        
        print("\n3️⃣ 执行任务...")
        result = agent._execute_task(analysis)
        print(f"📊 执行结果: {result}")
        
        print("\n4️⃣ 完整流程...")
        full_result = agent.process_task(task)
        print(f"📊 完整结果: {full_result}")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()


def check_abilities():
    """检查现有能力"""
    print("\n🔍 检查现有能力")
    
    agent = MasterAgent()
    abilities = agent.ability_manager.list_abilities()
    
    print(f"📊 总计 {len(abilities)} 个能力:")
    for ability in abilities:
        print(f"  ✅ {ability.name} ({ability.category})")
        if ability.code_path:
            print(f"     📁 代码路径: {ability.code_path}")


if __name__ == "__main__":
    check_abilities()
    debug_calculation()
