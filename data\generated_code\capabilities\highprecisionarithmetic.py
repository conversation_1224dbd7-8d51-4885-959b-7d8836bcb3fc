import decimal
import logging
from decimal import Decimal, getcontext
from typing import Any, Dict, List, Optional


class HighPrecisionArithmetic:
    """
    A class designed to perform high-precision arithmetic operations, including addition,
    subtraction, multiplication, division, and more, with configurable precision levels.

    This class is useful for applications requiring calculations beyond standard
    floating-point precision, such as financial computations, scientific research,
    or cryptographic algorithms.

    Attributes:
        precision (int): The number of significant digits used in arithmetic operations.
        rounding (str): The rounding mode used in arithmetic operations.
        logger (logging.Logger): Logger instance for tracking operations.
    """

    def __init__(self, precision: int = 28, rounding: str = "ROUND_HALF_EVEN") -> None:
        """
        Initialize the HighPrecisionArithmetic class with specified precision and rounding mode.

        Args:
            precision: The number of significant digits to use. Defaults to 28.
            rounding: The rounding mode to apply. Defaults to "ROUND_HALF_EVEN".

        Raises:
            ValueError: If precision is less than 1 or rounding mode is invalid.
        """
        if precision < 1:
            raise ValueError("Precision must be at least 1.")

        valid_rounding_modes = [
            "ROUND_CEILING",
            "ROUND_DOWN",
            "ROUND_FLOOR",
            "ROUND_HALF_DOWN",
            "ROUND_HALF_EVEN",
            "ROUND_HALF_UP",
            "ROUND_UP",
            "ROUND_05UP",
        ]

        if rounding not in valid_rounding_modes:
            raise ValueError(
                f"Invalid rounding mode. Must be one of {valid_rounding_modes}."
            )

        self.precision = precision
        self.rounding = rounding
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)

        # Configure decimal context
        getcontext().prec = self.precision
        getcontext().rounding = rounding

        self.logger.info(
            f"Initialized HighPrecisionArithmetic with precision={precision} and rounding={rounding}"
        )

    def add(self, a: Any, b: Any) -> Decimal:
        """
        Perform high-precision addition of two numbers.

        Args:
            a: First operand.
            b: Second operand.

        Returns:
            Decimal: The result of a + b with configured precision.

        Raises:
            TypeError: If operands cannot be converted to Decimal.
        """
        try:
            num1 = Decimal(str(a))
            num2 = Decimal(str(b))
            result = num1 + num2
            self.logger.debug(f"Performed addition: {num1} + {num2} = {result}")
            return result
        except (decimal.InvalidOperation, TypeError) as e:
            self.logger.error(f"Error in addition: {e}")
            raise TypeError(f"Operands must be convertible to Decimal: {e}")

    def subtract(self, a: Any, b: Any) -> Decimal:
        """
        Perform high-precision subtraction of two numbers.

        Args:
            a: First operand.
            b: Second operand.

        Returns:
            Decimal: The result of a - b with configured precision.

        Raises:
            TypeError: If operands cannot be converted to Decimal.
        """
        try:
            num1 = Decimal(str(a))
            num2 = Decimal(str(b))
            result = num1 - num2
            self.logger.debug(f"Performed subtraction: {num1} - {num2} = {result}")
            return result
        except (decimal.InvalidOperation, TypeError) as e:
            self.logger.error(f"Error in subtraction: {e}")
            raise TypeError(f"Operands must be convertible to Decimal: {e}")

    def multiply(self, a: Any, b: Any) -> Decimal:
        """
        Perform high-precision multiplication of two numbers.

        Args:
            a: First operand.
            b: Second operand.

        Returns:
            Decimal: The result of a * b with configured precision.

        Raises:
            TypeError: If operands cannot be converted to Decimal.
        """
        try:
            num1 = Decimal(str(a))
            num2 = Decimal(str(b))
            result = num1 * num2
            self.logger.debug(f"Performed multiplication: {num1} * {num2} = {result}")
            return result
        except (decimal.InvalidOperation, TypeError) as e:
            self.logger.error(f"Error in multiplication: {e}")
            raise TypeError(f"Operands must be convertible to Decimal: {e}")

    def divide(self, a: Any, b: Any) -> Decimal:
        """
        Perform high-precision division of two numbers.

        Args:
            a: Dividend.
            b: Divisor.

        Returns:
            Decimal: The result of a / b with configured precision.

        Raises:
            TypeError: If operands cannot be converted to Decimal.
            ZeroDivisionError: If divisor is zero.
        """
        try:
            num1 = Decimal(str(a))
            num2 = Decimal(str(b))
            if num2 == Decimal(0):
                raise ZeroDivisionError("Division by zero is not allowed.")
            result = num1 / num2
            self.logger.debug(f"Performed division: {num1} / {num2} = {result}")
            return result
        except (decimal.InvalidOperation, TypeError) as e:
            self.logger.error(f"Error in division: {e}")
            raise TypeError(f"Operands must be convertible to Decimal: {e}")
        except ZeroDivisionError as e:
            self.logger.error(f"Division by zero: {e}")
            raise

    def power(self, base: Any, exponent: Any) -> Decimal:
        """
        Perform high-precision exponentiation.

        Args:
            base: The base number.
            exponent: The exponent.

        Returns:
            Decimal: The result of base^exponent with configured precision.

        Raises:
            TypeError: If operands cannot be converted to Decimal.
        """
        try:
            base_num = Decimal(str(base))
            exponent_num = Decimal(str(exponent))
            result = base_num**exponent_num
            self.logger.debug(
                f"Performed exponentiation: {base_num}^{exponent_num} = {result}"
            )
            return result
        except (decimal.InvalidOperation, TypeError) as e:
            self.logger.error(f"Error in exponentiation: {e}")
            raise TypeError(f"Operands must be convertible to Decimal: {e}")

    def sqrt(self, a: Any) -> Decimal:
        """
        Perform high-precision square root.

        Args:
            a: The number to compute the square root of.

        Returns:
            Decimal: The square root of a with configured precision.

        Raises:
            TypeError: If operand cannot be converted to Decimal.
            ValueError: If operand is negative.
        """
        try:
            num = Decimal(str(a))
            if num < Decimal(0):
                raise ValueError("Square root of a negative number is not real.")
            result = num.sqrt()
            self.logger.debug(f"Performed square root: sqrt({num}) = {result}")
            return result
        except (decimal.InvalidOperation, TypeError) as e:
            self.logger.error(f"Error in square root: {e}")
            raise TypeError(f"Operand must be convertible to Decimal: {e}")
        except ValueError as e:
            self.logger.error(f"Invalid value for square root: {e}")
            raise

    def set_precision(self, precision: int) -> None:
        """
        Set the precision for arithmetic operations.

        Args:
            precision: The number of significant digits to use.

        Raises:
            ValueError: If precision is less than 1.
        """
        if precision < 1:
            raise ValueError("Precision must be at least 1.")
        self.precision = precision
        getcontext().prec = precision
        self.logger.info(f"Precision set to {precision}")

    def set_rounding(self, rounding: str) -> None:
        """
        Set the rounding mode for arithmetic operations.

        Args:
            rounding: The rounding mode to apply.

        Raises:
            ValueError: If rounding mode is invalid.
        """
        valid_rounding_modes = [
            "ROUND_CEILING",
            "ROUND_DOWN",
            "ROUND_FLOOR",
            "ROUND_HALF_DOWN",
            "ROUND_HALF_EVEN",
            "ROUND_HALF_UP",
            "ROUND_UP",
            "ROUND_05UP",
        ]

        if rounding not in valid_rounding_modes:
            raise ValueError(
                f"Invalid rounding mode. Must be one of {valid_rounding_modes}."
            )

        self.rounding = rounding
        getcontext().rounding = rounding
        self.logger.info(f"Rounding mode set to {rounding}")
