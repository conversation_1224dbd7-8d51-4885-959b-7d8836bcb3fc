#!/usr/bin/env python3
"""Comprehensive demonstration of the self-expanding agent system."""

import time
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

from src.self_expanding_agent.core.master_agent import MasterAgent
from src.self_expanding_agent.extensions.api_discovery import APIDiscovery
from src.self_expanding_agent.extensions.dependency_manager import DependencyManager
from src.self_expanding_agent.extensions.module_integrator import ModuleIntegrator
from src.self_expanding_agent.workflow.dynamic_workflow import DynamicWorkflow

console = Console()


def demo_header(title: str):
    """Display a demo section header."""
    console.print(Panel(title, style="bold blue"))


def demo_task_processing():
    """Demonstrate basic task processing and capability expansion."""
    demo_header("🎯 演示1: 任务处理与能力扩展")
    
    agent = MasterAgent()
    
    tasks = [
        "分析文本'今天天气真好'的情感倾向",
        "将摄氏温度25度转换为华氏温度",
        "计算圆周率π的前10位小数",
        "生成一个包含5个随机数的列表"
    ]
    
    for i, task in enumerate(tasks, 1):
        console.print(f"\n📋 任务 {i}: {task}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task_progress = progress.add_task("处理中...", total=None)
            
            result = agent.process_task(task)
            
            progress.update(task_progress, completed=True)
        
        console.print(f"✅ 结果: {result['success']}")
        if result.get('new_capabilities_added'):
            console.print(f"🆕 新增能力: {', '.join(result['new_capabilities_added'])}")
        
        time.sleep(1)


def demo_api_discovery():
    """Demonstrate API discovery functionality."""
    demo_header("🔍 演示2: API发现与集成")
    
    api_discovery = APIDiscovery()
    
    queries = [
        "weather API",
        "currency conversion",
        "text translation",
        "image processing"
    ]
    
    for query in queries:
        console.print(f"\n🔎 搜索: {query}")
        
        try:
            apis = api_discovery.discover_apis(query, max_results=3)
            
            if apis:
                console.print(f"✅ 发现 {len(apis)} 个相关API:")
                for api in apis:
                    console.print(f"  • {api['name']}: {api['description'][:100]}...")
            else:
                console.print("❌ 未发现相关API")
                
        except Exception as e:
            console.print(f"❌ API发现失败: {e}")
        
        time.sleep(0.5)


def demo_dependency_management():
    """Demonstrate dependency management."""
    demo_header("📦 演示3: 依赖管理")
    
    dep_manager = DependencyManager()
    
    # 模拟依赖需求
    dependencies = [
        "requests",
        "numpy", 
        "pandas",
        "matplotlib"
    ]
    
    for dep in dependencies:
        console.print(f"\n📋 检查依赖: {dep}")
        
        try:
            info = dep_manager.get_package_info(dep)
            console.print(f"✅ 包信息: {info['name']} v{info['version']}")
            console.print(f"📝 描述: {info['description'][:100]}...")
            
        except Exception as e:
            console.print(f"❌ 获取包信息失败: {e}")
        
        time.sleep(0.5)


def demo_module_integration():
    """Demonstrate module integration."""
    demo_header("🔧 演示4: 模块集成")
    
    integrator = ModuleIntegrator()
    
    # 创建一个简单的示例模块
    sample_code = '''
def calculate_fibonacci(n: int) -> list:
    """Calculate Fibonacci sequence up to n terms."""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    fib = [0, 1]
    for i in range(2, n):
        fib.append(fib[i-1] + fib[i-2])
    
    return fib

def is_prime(num: int) -> bool:
    """Check if a number is prime."""
    if num < 2:
        return False
    for i in range(2, int(num ** 0.5) + 1):
        if num % i == 0:
            return False
    return True
'''
    
    console.print("\n🔧 集成数学计算模块...")
    
    try:
        success = integrator.integrate_capability(
            code=sample_code,
            capability_name="math_utilities",
            description="数学工具函数集合",
            auto_install_deps=False
        )
        
        if success:
            console.print("✅ 模块集成成功!")
            
            # 测试集成的功能
            console.print("\n🧪 测试集成的功能:")
            
            try:
                # 测试斐波那契数列
                result = integrator.call_capability_function(
                    [10],
                    capability_name="math_utilities",
                    function_name="calculate_fibonacci"
                )
                console.print(f"📊 斐波那契数列(10项): {result}")
                
                # 测试质数检查
                result = integrator.call_capability_function(
                    [17],
                    capability_name="math_utilities", 
                    function_name="is_prime"
                )
                console.print(f"🔢 17是质数吗: {result}")
                
            except Exception as e:
                console.print(f"❌ 功能测试失败: {e}")
        else:
            console.print("❌ 模块集成失败")
            
    except Exception as e:
        console.print(f"❌ 集成过程出错: {e}")


def demo_workflow_generation():
    """Demonstrate dynamic workflow generation."""
    demo_header("🔄 演示5: 动态工作流生成")
    
    workflow_system = DynamicWorkflow()
    
    complex_tasks = [
        "分析用户评论的情感，然后生成回复建议",
        "获取天气数据，分析趋势，生成可视化图表",
        "处理CSV数据文件，清洗数据，计算统计信息"
    ]
    
    for i, task in enumerate(complex_tasks, 1):
        console.print(f"\n🎯 复杂任务 {i}: {task}")
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                workflow_progress = progress.add_task("生成工作流...", total=None)
                
                result = workflow_system.create_and_execute_workflow(
                    task_description=task,
                    complexity=7
                )
                
                progress.update(workflow_progress, completed=True)
            
            console.print(f"✅ 工作流执行: {result.get('success', False)}")
            
        except Exception as e:
            console.print(f"❌ 工作流生成失败: {e}")
        
        time.sleep(1)


def main():
    """Run comprehensive demonstration."""
    console.print(Panel.fit(
        "🚀 自扩展智能体系统 - 综合功能演示",
        style="bold green"
    ))
    
    console.print("\n这个演示将展示系统的核心功能:")
    console.print("1. 任务处理与自动能力扩展")
    console.print("2. API发现与集成")
    console.print("3. 依赖管理")
    console.print("4. 模块集成")
    console.print("5. 动态工作流生成")
    
    input("\n按回车键开始演示...")
    
    try:
        demo_task_processing()
        input("\n按回车键继续下一个演示...")
        
        demo_api_discovery()
        input("\n按回车键继续下一个演示...")
        
        demo_dependency_management()
        input("\n按回车键继续下一个演示...")
        
        demo_module_integration()
        input("\n按回车键继续下一个演示...")
        
        demo_workflow_generation()
        
        console.print(Panel.fit(
            "🎉 演示完成！\n\n系统成功展示了自扩展智能体的核心功能:\n"
            "✅ 自动任务分析和能力扩展\n"
            "✅ 动态代码生成和集成\n"
            "✅ API发现和依赖管理\n"
            "✅ 模块化架构和工作流协调",
            style="bold green"
        ))
        
    except KeyboardInterrupt:
        console.print("\n\n⚠️ 演示被用户中断")
    except Exception as e:
        console.print(f"\n\n❌ 演示过程中出现错误: {e}")


if __name__ == "__main__":
    main()
