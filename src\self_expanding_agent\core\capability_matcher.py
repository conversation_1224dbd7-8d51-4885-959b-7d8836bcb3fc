#!/usr/bin/env python3
"""
智能能力匹配器 - 负责发现和匹配合适的能力来执行任务
"""

import logging
from typing import Dict, List, Any, Optional
from src.self_expanding_agent.core.ability_manager import AbilityManager

logger = logging.getLogger(__name__)


class CapabilityMatcher:
    """智能能力匹配器，用于发现和匹配合适的能力来执行任务"""
    
    def __init__(self, ability_manager: AbilityManager):
        """初始化能力匹配器
        
        Args:
            ability_manager: 能力管理器实例
        """
        self.ability_manager = ability_manager
        logger.info("Initialized CapabilityMatcher")
    
    def find_suitable_capabilities(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据任务分析结果查找合适的能力
        
        Args:
            analysis: 任务分析结果
            
        Returns:
            匹配的能力列表，每个能力包含详细信息
        """
        logger.info("Starting capability matching process")
        
        # 获取所有可用能力
        all_abilities = self.ability_manager.list_abilities()
        active_abilities = [ability for ability in all_abilities if ability.is_active and ability.code_path]
        
        logger.info(f"Found {len(active_abilities)} active capabilities with code")
        
        # 提取任务信息
        task_type = analysis.get("task_type", "").lower()
        category = analysis.get("category", "").lower()
        required_capabilities = analysis.get("required_capabilities", [])
        
        matched_capabilities = []
        
        # 1. 精确匹配：基于required_capabilities
        for req_cap in required_capabilities:
            req_cap_lower = req_cap.lower()
            for ability in active_abilities:
                if self._is_capability_match(ability.name.lower(), req_cap_lower):
                    matched_capabilities.append({
                        "name": ability.name,
                        "description": ability.description,
                        "category": ability.category,
                        "code_path": ability.code_path,
                        "match_type": "exact_requirement",
                        "match_score": 1.0
                    })
                    logger.info(f"Exact match found: {ability.name} for requirement: {req_cap}")
        
        # 2. 类别匹配：基于category
        if category:
            for ability in active_abilities:
                if ability.category.lower() == category:
                    # 避免重复添加
                    if not any(cap["name"] == ability.name for cap in matched_capabilities):
                        matched_capabilities.append({
                            "name": ability.name,
                            "description": ability.description,
                            "category": ability.category,
                            "code_path": ability.code_path,
                            "match_type": "category_match",
                            "match_score": 0.8
                        })
                        logger.info(f"Category match found: {ability.name} for category: {category}")
        
        # 3. 任务类型匹配：基于task_type
        if task_type:
            for ability in active_abilities:
                if self._is_task_type_match(ability.name.lower(), ability.description.lower(), task_type):
                    # 避免重复添加
                    if not any(cap["name"] == ability.name for cap in matched_capabilities):
                        matched_capabilities.append({
                            "name": ability.name,
                            "description": ability.description,
                            "category": ability.category,
                            "code_path": ability.code_path,
                            "match_type": "task_type_match",
                            "match_score": 0.6
                        })
                        logger.info(f"Task type match found: {ability.name} for task type: {task_type}")
        
        # 4. 语义匹配：基于关键词
        keywords = self._extract_keywords(analysis)
        for ability in active_abilities:
            if self._has_keyword_match(ability.name.lower(), ability.description.lower(), keywords):
                # 避免重复添加
                if not any(cap["name"] == ability.name for cap in matched_capabilities):
                    matched_capabilities.append({
                        "name": ability.name,
                        "description": ability.description,
                        "category": ability.category,
                        "code_path": ability.code_path,
                        "match_type": "keyword_match",
                        "match_score": 0.4
                    })
                    logger.info(f"Keyword match found: {ability.name}")
        
        # 按匹配分数排序
        matched_capabilities.sort(key=lambda x: x["match_score"], reverse=True)
        
        logger.info(f"Found {len(matched_capabilities)} matching capabilities")
        return matched_capabilities
    
    def _is_capability_match(self, ability_name: str, required_capability: str) -> bool:
        """检查能力名称是否匹配所需能力
        
        Args:
            ability_name: 能力名称
            required_capability: 所需能力
            
        Returns:
            是否匹配
        """
        # 直接匹配
        if ability_name == required_capability:
            return True
        
        # 包含匹配
        if required_capability in ability_name or ability_name in required_capability:
            return True
        
        # 关键词匹配
        ability_words = set(ability_name.replace("_", " ").replace("-", " ").split())
        required_words = set(required_capability.replace("_", " ").replace("-", " ").split())
        
        # 如果有超过一半的词匹配，认为是匹配的
        common_words = ability_words.intersection(required_words)
        if len(common_words) > 0 and len(common_words) >= len(required_words) * 0.5:
            return True
        
        return False
    
    def _is_task_type_match(self, ability_name: str, ability_description: str, task_type: str) -> bool:
        """检查能力是否匹配任务类型
        
        Args:
            ability_name: 能力名称
            ability_description: 能力描述
            task_type: 任务类型
            
        Returns:
            是否匹配
        """
        task_keywords = task_type.replace("_", " ").replace("-", " ").split()
        
        for keyword in task_keywords:
            if keyword in ability_name or keyword in ability_description:
                return True
        
        return False
    
    def _extract_keywords(self, analysis: Dict[str, Any]) -> List[str]:
        """从任务分析中提取关键词
        
        Args:
            analysis: 任务分析结果
            
        Returns:
            关键词列表
        """
        keywords = []
        
        # 从各个字段提取关键词
        for field in ["task_type", "category", "sub_tasks", "inputs", "outputs"]:
            value = analysis.get(field, "")
            if isinstance(value, str):
                keywords.extend(value.lower().replace("_", " ").replace("-", " ").split())
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, str):
                        keywords.extend(item.lower().replace("_", " ").replace("-", " ").split())
        
        # 去重并过滤常见词
        common_words = {"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        keywords = list(set(keywords) - common_words)
        
        return keywords
    
    def _has_keyword_match(self, ability_name: str, ability_description: str, keywords: List[str]) -> bool:
        """检查能力是否与关键词匹配
        
        Args:
            ability_name: 能力名称
            ability_description: 能力描述
            keywords: 关键词列表
            
        Returns:
            是否匹配
        """
        ability_text = f"{ability_name} {ability_description}".lower()
        
        for keyword in keywords:
            if keyword in ability_text:
                return True
        
        return False
