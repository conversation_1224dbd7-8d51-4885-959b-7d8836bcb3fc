"""API discovery system for finding and analyzing external APIs."""

import json
import re
from typing import Any, Dict, List, Optional, Tuple

import requests
from openai import OpenAI
from pydantic import BaseModel, Field

from ..utils.config import config
from ..utils.decorators import retry
from ..utils.logger import logger


class APIEndpoint(BaseModel):
    """Represents an API endpoint."""
    name: str
    url: str
    method: str = "GET"
    description: str
    parameters: List[Dict[str, Any]] = Field(default_factory=list)
    headers: Dict[str, str] = Field(default_factory=dict)
    authentication: Optional[str] = None
    response_format: str = "json"
    rate_limit: Optional[str] = None


class APIService(BaseModel):
    """Represents an API service."""
    name: str
    base_url: str
    description: str
    category: str
    endpoints: List[APIEndpoint] = Field(default_factory=list)
    documentation_url: Optional[str] = None
    api_key_required: bool = False
    pricing: Optional[str] = None
    reliability_score: float = 0.0


class APIDiscovery:
    """Discovers and analyzes APIs for integration."""
    
    def __init__(self):
        """Initialize the API discovery system."""
        self.client = OpenAI(
            api_key=config.deepseek_api_key,
            base_url=config.deepseek_base_url
        )
        self.discovered_apis: Dict[str, APIService] = {}
        
        # 预定义一些常用API
        self._load_predefined_apis()
        
        logger.info("Initialized APIDiscovery system")
    
    def _load_predefined_apis(self) -> None:
        """Load predefined APIs for common use cases."""
        predefined_apis = [
            APIService(
                name="OpenWeatherMap",
                base_url="https://api.openweathermap.org/data/2.5",
                description="Weather data and forecasts",
                category="weather",
                api_key_required=True,
                endpoints=[
                    APIEndpoint(
                        name="current_weather",
                        url="/weather",
                        method="GET",
                        description="Get current weather for a location",
                        parameters=[
                            {"name": "q", "type": "string", "description": "City name"},
                            {"name": "appid", "type": "string", "description": "API key"}
                        ]
                    )
                ]
            ),
            APIService(
                name="JSONPlaceholder",
                base_url="https://jsonplaceholder.typicode.com",
                description="Fake REST API for testing and prototyping",
                category="testing",
                api_key_required=False,
                endpoints=[
                    APIEndpoint(
                        name="get_posts",
                        url="/posts",
                        method="GET",
                        description="Get all posts"
                    ),
                    APIEndpoint(
                        name="get_post",
                        url="/posts/{id}",
                        method="GET",
                        description="Get a specific post",
                        parameters=[
                            {"name": "id", "type": "integer", "description": "Post ID"}
                        ]
                    )
                ]
            )
        ]
        
        for api in predefined_apis:
            self.discovered_apis[api.name] = api
    
    @retry()
    def discover_apis_for_capability(self, capability: str) -> List[APIService]:
        """Discover APIs that could provide a specific capability.
        
        Args:
            capability: Capability description
            
        Returns:
            List of relevant API services
        """
        logger.info(f"Discovering APIs for capability: {capability}")
        
        # 使用LLM分析能力需求并推荐API
        api_recommendations = self._analyze_capability_requirements(capability)
        
        discovered_apis = []
        for recommendation in api_recommendations:
            try:
                api_service = self._research_api(recommendation)
                if api_service:
                    discovered_apis.append(api_service)
                    self.discovered_apis[api_service.name] = api_service
            except Exception as e:
                logger.warning(f"Failed to research API {recommendation}: {e}")
        
        # 也检查已知的API
        relevant_existing = self._find_relevant_existing_apis(capability)
        discovered_apis.extend(relevant_existing)
        
        logger.info(f"Discovered {len(discovered_apis)} APIs for capability: {capability}")
        return discovered_apis
    
    def _analyze_capability_requirements(self, capability: str) -> List[str]:
        """Analyze capability requirements and suggest APIs.
        
        Args:
            capability: Capability description
            
        Returns:
            List of API service names or descriptions
        """
        prompt = f"""Analyze the following capability requirement and suggest relevant APIs or services:

Capability: {capability}

Please suggest specific APIs, web services, or data sources that could help implement this capability. 
Focus on:
1. Well-known public APIs
2. Free or freemium services when possible
3. Reliable and well-documented services
4. Services with good developer support

For each suggestion, provide:
- Service/API name
- Brief description of how it helps with the capability
- Whether it requires an API key
- Approximate reliability (high/medium/low)

Format your response as a JSON array of objects:
[
    {{
        "name": "API Name",
        "description": "How it helps with the capability",
        "requires_key": true/false,
        "reliability": "high/medium/low",
        "category": "category_name"
    }}
]

Respond only with the JSON array."""
        
        try:
            response = self.client.chat.completions.create(
                model=config.default_model,
                messages=[
                    {"role": "system", "content": "You are an expert in APIs and web services. Provide accurate, helpful API recommendations."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1500
            )
            
            recommendations = json.loads(response.choices[0].message.content)
            return [rec["name"] for rec in recommendations]
            
        except json.JSONDecodeError:
            logger.warning("Failed to parse API recommendations JSON")
            return []
        except Exception as e:
            logger.error(f"Failed to analyze capability requirements: {e}")
            return []
    
    def _research_api(self, api_name: str) -> Optional[APIService]:
        """Research a specific API and gather information.
        
        Args:
            api_name: Name of the API to research
            
        Returns:
            APIService object or None if research failed
        """
        research_prompt = f"""Research the API: {api_name}

Provide detailed information about this API including:
1. Official name and base URL
2. Description and main use cases
3. Authentication requirements
4. Key endpoints and their purposes
5. Request/response formats
6. Rate limits if known
7. Pricing model (free/paid/freemium)
8. Documentation quality and availability

Format your response as JSON:
{{
    "name": "Official API Name",
    "base_url": "https://api.example.com",
    "description": "What the API does",
    "category": "category",
    "api_key_required": true/false,
    "documentation_url": "https://docs.example.com",
    "pricing": "free/paid/freemium description",
    "reliability_score": 0.8,
    "endpoints": [
        {{
            "name": "endpoint_name",
            "url": "/endpoint/path",
            "method": "GET/POST/etc",
            "description": "What this endpoint does",
            "parameters": [
                {{"name": "param", "type": "string", "description": "Parameter description"}}
            ]
        }}
    ]
}}

If you cannot find reliable information about this API, respond with null."""
        
        try:
            response = self.client.chat.completions.create(
                model=config.default_model,
                messages=[
                    {"role": "system", "content": "You are an API research expert. Provide accurate, up-to-date information about APIs."},
                    {"role": "user", "content": research_prompt}
                ],
                temperature=0.2,
                max_tokens=2000
            )
            
            api_data = json.loads(response.choices[0].message.content)
            if api_data is None:
                return None
            
            # 转换为APIService对象
            endpoints = []
            for ep_data in api_data.get("endpoints", []):
                endpoint = APIEndpoint(
                    name=ep_data["name"],
                    url=ep_data["url"],
                    method=ep_data.get("method", "GET"),
                    description=ep_data["description"],
                    parameters=ep_data.get("parameters", [])
                )
                endpoints.append(endpoint)
            
            api_service = APIService(
                name=api_data["name"],
                base_url=api_data["base_url"],
                description=api_data["description"],
                category=api_data.get("category", "general"),
                endpoints=endpoints,
                documentation_url=api_data.get("documentation_url"),
                api_key_required=api_data.get("api_key_required", False),
                pricing=api_data.get("pricing"),
                reliability_score=api_data.get("reliability_score", 0.5)
            )
            
            return api_service
            
        except json.JSONDecodeError:
            logger.warning(f"Failed to parse API research results for: {api_name}")
            return None
        except Exception as e:
            logger.error(f"Failed to research API {api_name}: {e}")
            return None
    
    def _find_relevant_existing_apis(self, capability: str) -> List[APIService]:
        """Find relevant APIs from already discovered ones.
        
        Args:
            capability: Capability description
            
        Returns:
            List of relevant API services
        """
        relevant_apis = []
        capability_lower = capability.lower()
        
        for api_service in self.discovered_apis.values():
            # 检查API描述和类别是否与能力相关
            if (capability_lower in api_service.description.lower() or
                capability_lower in api_service.category.lower()):
                relevant_apis.append(api_service)
                continue
            
            # 检查端点描述
            for endpoint in api_service.endpoints:
                if capability_lower in endpoint.description.lower():
                    relevant_apis.append(api_service)
                    break
        
        return relevant_apis
    
    def test_api_endpoint(self, api_service: APIService, endpoint_name: str, test_params: Optional[Dict[str, Any]] = None) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Test an API endpoint to verify it works.
        
        Args:
            api_service: API service to test
            endpoint_name: Name of the endpoint to test
            test_params: Optional test parameters
            
        Returns:
            Tuple of (success, response_data)
        """
        endpoint = None
        for ep in api_service.endpoints:
            if ep.name == endpoint_name:
                endpoint = ep
                break
        
        if not endpoint:
            logger.error(f"Endpoint {endpoint_name} not found in API {api_service.name}")
            return False, None
        
        try:
            url = api_service.base_url + endpoint.url
            
            # 替换URL参数
            if test_params:
                for param, value in test_params.items():
                    url = url.replace(f"{{{param}}}", str(value))
            
            # 准备请求
            headers = endpoint.headers.copy()
            params = {}
            
            if test_params:
                # 分离URL参数和查询参数
                for param in endpoint.parameters:
                    param_name = param["name"]
                    if param_name in test_params and f"{{{param_name}}}" not in endpoint.url:
                        params[param_name] = test_params[param_name]
            
            # 发送请求
            response = requests.request(
                method=endpoint.method,
                url=url,
                headers=headers,
                params=params if endpoint.method == "GET" else None,
                json=params if endpoint.method in ["POST", "PUT", "PATCH"] else None,
                timeout=10
            )
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    logger.info(f"API test successful: {api_service.name}.{endpoint_name}")
                    return True, response_data
                except json.JSONDecodeError:
                    logger.info(f"API test successful (non-JSON response): {api_service.name}.{endpoint_name}")
                    return True, {"response_text": response.text}
            else:
                logger.warning(f"API test failed: {api_service.name}.{endpoint_name}, Status: {response.status_code}")
                return False, {"status_code": response.status_code, "error": response.text}
                
        except Exception as e:
            logger.error(f"API test error: {api_service.name}.{endpoint_name}, Error: {e}")
            return False, {"error": str(e)}
    
    def generate_api_integration_code(self, api_service: APIService, endpoint_name: str) -> str:
        """Generate Python code to integrate with an API endpoint.
        
        Args:
            api_service: API service
            endpoint_name: Endpoint name
            
        Returns:
            Generated Python code
        """
        endpoint = None
        for ep in api_service.endpoints:
            if ep.name == endpoint_name:
                endpoint = ep
                break
        
        if not endpoint:
            raise ValueError(f"Endpoint {endpoint_name} not found")
        
        code_prompt = f"""Generate Python code to integrate with this API endpoint:

API: {api_service.name}
Base URL: {api_service.base_url}
Endpoint: {endpoint.name}
URL: {endpoint.url}
Method: {endpoint.method}
Description: {endpoint.description}
Parameters: {endpoint.parameters}
Requires API Key: {api_service.api_key_required}

Generate a Python function that:
1. Takes the required parameters as function arguments
2. Handles authentication if needed
3. Makes the API request
4. Handles errors gracefully
5. Returns the response data in a useful format
6. Includes proper documentation and type hints

The function should be production-ready with error handling and logging."""
        
        try:
            response = self.client.chat.completions.create(
                model=config.default_model,
                messages=[
                    {"role": "system", "content": "You are an expert Python developer. Generate clean, robust API integration code."},
                    {"role": "user", "content": code_prompt}
                ],
                temperature=0.3,
                max_tokens=2000
            )
            
            code = response.choices[0].message.content.strip()
            
            # 清理代码格式
            if code.startswith("```python"):
                code = code[9:]
            if code.endswith("```"):
                code = code[:-3]
            
            logger.info(f"Generated integration code for: {api_service.name}.{endpoint_name}")
            return code
            
        except Exception as e:
            logger.error(f"Failed to generate integration code: {e}")
            raise
    
    def get_api_service(self, name: str) -> Optional[APIService]:
        """Get a discovered API service by name.
        
        Args:
            name: API service name
            
        Returns:
            API service or None if not found
        """
        return self.discovered_apis.get(name)
    
    def list_discovered_apis(self, category: Optional[str] = None) -> List[APIService]:
        """List all discovered APIs, optionally filtered by category.
        
        Args:
            category: Optional category filter
            
        Returns:
            List of API services
        """
        if category:
            return [api for api in self.discovered_apis.values() if api.category == category]
        return list(self.discovered_apis.values())
