import logging
from typing import Any, Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def perform_arithmetic_operation(num1: float, num2: float, operation: str) -> float:
    """
    Performs basic arithmetic operations (addition, subtraction, multiplication, division) on two numbers.

    Args:
        num1: The first number for the arithmetic operation.
        num2: The second number for the arithmetic operation.
        operation: The arithmetic operation to perform. Valid values are 'add', 'subtract', 'multiply', 'divide'.

    Returns:
        float: The result of the arithmetic operation.

    Raises:
        ValueError: If the operation is not one of the valid values or if division by zero occurs.
    """
    try:
        logger.info(f"Performing {operation} operation on {num1} and {num2}")

        if operation == "add":
            result = num1 + num2
        elif operation == "subtract":
            result = num1 - num2
        elif operation == "multiply":
            result = num1 * num2
        elif operation == "divide":
            if num2 == 0:
                logger.error("Division by zero error")
                raise ValueError("Cannot divide by zero")
            result = num1 / num2
        else:
            logger.error(f"Invalid operation: {operation}")
            raise ValueError(
                f"Invalid operation: {operation}. Valid operations are 'add', 'subtract', 'multiply', 'divide'"
            )

        logger.info(f"Operation {operation} completed successfully. Result: {result}")
        return result

    except Exception as e:
        logger.error(f"Error during arithmetic operation: {e}")
        raise
