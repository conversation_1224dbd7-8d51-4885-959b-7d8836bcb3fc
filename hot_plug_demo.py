#!/usr/bin/env python3
"""演示自扩展智能体系统的热插拔功能"""

import time
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

from src.self_expanding_agent.core.ability_manager import AbilityManager
from src.self_expanding_agent.core.master_agent import MasterAgent

console = Console()


def display_abilities_table(ability_manager: AbilityManager):
    """显示能力状态表格"""
    # 获取所有能力（包括活跃和非活跃的）
    active_abilities = ability_manager.list_abilities()
    inactive_abilities = ability_manager.list_inactive_abilities()
    all_abilities = active_abilities + inactive_abilities
    
    if not all_abilities:
        console.print("❌ 没有找到任何能力")
        return
    
    table = Table(title="🧠 系统能力状态")
    table.add_column("能力名称", style="cyan", no_wrap=True)
    table.add_column("类别", style="magenta")
    table.add_column("状态", justify="center")
    table.add_column("描述", style="green")
    
    for ability in all_abilities:
        status = "✅ 活跃" if ability.is_active else "❌ 停用"
        # 截断长描述
        desc = ability.description[:50] + "..." if len(ability.description) > 50 else ability.description
        table.add_row(
            ability.name,
            ability.category,
            status,
            desc
        )
    
    console.print(table)


def demo_hot_plug_operations():
    """演示热插拔操作"""
    console.print(Panel.fit("🔌 热插拔功能演示", style="bold blue"))
    
    ability_manager = AbilityManager()
    
    # 显示当前状态
    console.print("\n📊 当前系统能力状态:")
    display_abilities_table(ability_manager)
    
    # 获取一些能力进行演示
    active_abilities = ability_manager.list_abilities()
    if not active_abilities:
        console.print("❌ 没有活跃的能力可以演示")
        return
    
    # 选择前几个能力进行演示
    demo_abilities = active_abilities[:3]
    
    console.print(f"\n🎯 将演示对以下能力的热插拔操作:")
    for i, ability in enumerate(demo_abilities, 1):
        console.print(f"  {i}. {ability.name}")
    
    input("\n按回车键开始演示...")
    
    # 演示停用能力
    console.print("\n🔌 演示1: 热停用能力")
    for ability in demo_abilities:
        console.print(f"\n⏸️  停用能力: {ability.name}")
        success = ability_manager.deactivate_ability(ability.name)
        if success:
            console.print(f"✅ 成功停用: {ability.name}")
        else:
            console.print(f"❌ 停用失败: {ability.name}")
        time.sleep(1)
    
    console.print("\n📊 停用后的状态:")
    display_abilities_table(ability_manager)
    
    input("\n按回车键继续...")
    
    # 演示重新激活能力
    console.print("\n🔌 演示2: 热激活能力")
    for ability in demo_abilities:
        console.print(f"\n▶️  激活能力: {ability.name}")
        success = ability_manager.activate_ability(ability.name)
        if success:
            console.print(f"✅ 成功激活: {ability.name}")
        else:
            console.print(f"❌ 激活失败: {ability.name}")
        time.sleep(1)
    
    console.print("\n📊 激活后的状态:")
    display_abilities_table(ability_manager)
    
    input("\n按回车键继续...")
    
    # 演示切换功能
    console.print("\n🔌 演示3: 切换能力状态")
    for ability in demo_abilities[:2]:  # 只演示前两个
        console.print(f"\n🔄 切换能力: {ability.name}")
        
        # 显示当前状态
        current_status = ability_manager.get_ability_status(ability.name)
        status_text = "活跃" if current_status else "停用"
        console.print(f"   当前状态: {status_text}")
        
        # 切换状态
        success = ability_manager.toggle_ability(ability.name)
        if success:
            new_status = ability_manager.get_ability_status(ability.name)
            new_status_text = "活跃" if new_status else "停用"
            console.print(f"✅ 切换成功，新状态: {new_status_text}")
        else:
            console.print(f"❌ 切换失败")
        time.sleep(1)
    
    console.print("\n📊 切换后的状态:")
    display_abilities_table(ability_manager)


def demo_runtime_capability_impact():
    """演示运行时能力变化对系统的影响"""
    console.print(Panel.fit("⚡ 运行时影响演示", style="bold green"))
    
    ability_manager = AbilityManager()
    agent = MasterAgent()
    
    # 选择一个能力进行演示
    abilities = ability_manager.list_abilities()
    if not abilities:
        console.print("❌ 没有可用的能力")
        return
    
    # 选择算术能力进行演示
    target_ability = None
    for ability in abilities:
        if "arithmetic" in ability.name.lower() or "calculation" in ability.name.lower():
            target_ability = ability
            break
    
    if not target_ability:
        target_ability = abilities[0]  # 如果没找到算术能力，使用第一个
    
    console.print(f"\n🎯 将使用能力: {target_ability.name}")
    console.print(f"📝 描述: {target_ability.description}")
    
    # 测试任务
    test_task = "计算 15 + 27 的结果"
    
    console.print(f"\n📋 测试任务: {test_task}")
    
    # 第一次执行（能力可用）
    console.print("\n✅ 第一次执行（能力可用）:")
    try:
        result1 = agent.process_task(test_task)
        console.print(f"📊 执行结果: {result1['success']}")
    except Exception as e:
        console.print(f"❌ 执行失败: {e}")
    
    input("\n按回车键停用能力并重新测试...")
    
    # 停用能力
    console.print(f"\n⏸️  停用能力: {target_ability.name}")
    ability_manager.deactivate_ability(target_ability.name)
    
    # 第二次执行（能力不可用）
    console.print("\n❌ 第二次执行（能力已停用）:")
    try:
        result2 = agent.process_task(test_task)
        console.print(f"📊 执行结果: {result2['success']}")
        if result2.get('new_capabilities_added'):
            console.print(f"🆕 系统自动生成了新能力: {result2['new_capabilities_added']}")
    except Exception as e:
        console.print(f"❌ 执行失败: {e}")
    
    input("\n按回车键重新激活能力...")
    
    # 重新激活能力
    console.print(f"\n▶️  重新激活能力: {target_ability.name}")
    ability_manager.activate_ability(target_ability.name)
    
    # 第三次执行（能力重新可用）
    console.print("\n✅ 第三次执行（能力重新激活）:")
    try:
        result3 = agent.process_task(test_task)
        console.print(f"📊 执行结果: {result3['success']}")
    except Exception as e:
        console.print(f"❌ 执行失败: {e}")


def main():
    """主函数"""
    console.print(Panel.fit(
        "🔌 自扩展智能体系统 - 热插拔功能演示\n\n"
        "本演示将展示:\n"
        "1. 能力的热停用和热激活\n"
        "2. 能力状态的实时切换\n"
        "3. 运行时能力变化对系统的影响",
        style="bold cyan"
    ))
    
    try:
        # 演示基本热插拔操作
        demo_hot_plug_operations()
        
        input("\n按回车键继续运行时影响演示...")
        
        # 演示运行时影响
        demo_runtime_capability_impact()
        
        console.print(Panel.fit(
            "🎉 热插拔演示完成！\n\n"
            "✅ 系统支持完整的热插拔功能:\n"
            "• 能力可以在运行时动态停用和激活\n"
            "• 状态变化立即生效，无需重启\n"
            "• 系统能够自适应能力变化\n"
            "• 支持能力状态查询和管理",
            style="bold green"
        ))
        
    except KeyboardInterrupt:
        console.print("\n\n⚠️ 演示被用户中断")
    except Exception as e:
        console.print(f"\n\n❌ 演示过程中出现错误: {e}")


if __name__ == "__main__":
    main()
