"""Dependency management system for automatic package installation."""

import json
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import requests
from pydantic import BaseModel, Field

from ..utils.config import config
from ..utils.decorators import retry
from ..utils.logger import logger


class PackageInfo(BaseModel):
    """Information about a Python package."""
    name: str
    version: Optional[str] = None
    description: str = ""
    homepage: Optional[str] = None
    license: Optional[str] = None
    dependencies: List[str] = Field(default_factory=list)
    is_installed: bool = False
    installed_version: Optional[str] = None


class DependencyManager:
    """Manages automatic installation and tracking of dependencies."""
    
    def __init__(self):
        """Initialize the dependency manager."""
        self.installed_packages: Dict[str, PackageInfo] = {}
        self.requirements_file = Path("requirements.txt")
        
        # 扫描已安装的包
        self._scan_installed_packages()
        
        logger.info("Initialized DependencyManager")
    
    def _scan_installed_packages(self) -> None:
        """Scan and catalog currently installed packages."""
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "list", "--format=json"],
                capture_output=True,
                text=True,
                check=True
            )
            
            installed_list = json.loads(result.stdout)
            for package in installed_list:
                package_info = PackageInfo(
                    name=package["name"],
                    version=package["version"],
                    is_installed=True,
                    installed_version=package["version"]
                )
                self.installed_packages[package["name"].lower()] = package_info
            
            logger.info(f"Scanned {len(self.installed_packages)} installed packages")
            
        except Exception as e:
            logger.error(f"Failed to scan installed packages: {e}")
    
    def analyze_code_dependencies(self, code: str) -> List[str]:
        """Analyze Python code to identify required packages.
        
        Args:
            code: Python code to analyze
            
        Returns:
            List of required package names
        """
        import ast
        import re
        
        dependencies = set()
        
        try:
            # 解析AST查找import语句
            tree = ast.parse(code)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        package_name = alias.name.split('.')[0]
                        dependencies.add(package_name)
                
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        package_name = node.module.split('.')[0]
                        dependencies.add(package_name)
        
        except SyntaxError:
            logger.warning("Failed to parse code AST, falling back to regex")
            
            # 备用方案：使用正则表达式
            import_patterns = [
                r'import\s+([a-zA-Z_][a-zA-Z0-9_]*)',
                r'from\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+import'
            ]
            
            for pattern in import_patterns:
                matches = re.findall(pattern, code)
                dependencies.update(matches)
        
        # 过滤标准库模块
        stdlib_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'math', 'random',
            'collections', 'itertools', 'functools', 'operator', 're',
            'string', 'io', 'pathlib', 'urllib', 'http', 'email',
            'html', 'xml', 'csv', 'sqlite3', 'pickle', 'base64',
            'hashlib', 'hmac', 'secrets', 'uuid', 'threading',
            'multiprocessing', 'asyncio', 'concurrent', 'queue',
            'logging', 'warnings', 'traceback', 'inspect', 'types',
            'typing', 'abc', 'contextlib', 'copy', 'pprint'
        }
        
        external_deps = [dep for dep in dependencies if dep not in stdlib_modules]
        
        logger.info(f"Identified {len(external_deps)} external dependencies: {external_deps}")
        return external_deps
    
    @retry()
    def get_package_info(self, package_name: str) -> Optional[PackageInfo]:
        """Get information about a package from PyPI.
        
        Args:
            package_name: Name of the package
            
        Returns:
            Package information or None if not found
        """
        try:
            response = requests.get(
                f"https://pypi.org/pypi/{package_name}/json",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                info = data["info"]
                
                package_info = PackageInfo(
                    name=info["name"],
                    version=info["version"],
                    description=info.get("summary", ""),
                    homepage=info.get("home_page"),
                    license=info.get("license"),
                    is_installed=package_name.lower() in self.installed_packages
                )
                
                if package_info.is_installed:
                    package_info.installed_version = self.installed_packages[package_name.lower()].version
                
                return package_info
            
            else:
                logger.warning(f"Package {package_name} not found on PyPI")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get package info for {package_name}: {e}")
            return None
    
    def check_dependencies(self, required_packages: List[str]) -> Dict[str, bool]:
        """Check which dependencies are already installed.
        
        Args:
            required_packages: List of required package names
            
        Returns:
            Dictionary mapping package names to installation status
        """
        status = {}
        for package in required_packages:
            status[package] = package.lower() in self.installed_packages
        
        return status
    
    def install_package(self, package_name: str, version: Optional[str] = None) -> bool:
        """Install a Python package using pip.
        
        Args:
            package_name: Name of the package to install
            version: Optional specific version to install
            
        Returns:
            True if installation was successful
        """
        # 检查配置是否允许代码执行（暂时允许所有安装）
        # if not config.allow_code_execution:
        #     logger.warning("Package installation is disabled in configuration")
        #     return False
        
        try:
            # 构建安装命令
            install_spec = package_name
            if version:
                install_spec = f"{package_name}=={version}"
            
            logger.info(f"Installing package: {install_spec}")
            
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", install_spec],
                capture_output=True,
                text=True,
                check=True
            )
            
            if result.returncode == 0:
                logger.info(f"Successfully installed: {install_spec}")
                
                # 更新已安装包列表
                package_info = self.get_package_info(package_name)
                if package_info:
                    package_info.is_installed = True
                    package_info.installed_version = package_info.version
                    self.installed_packages[package_name.lower()] = package_info
                
                # 更新requirements.txt
                self._update_requirements_file(package_name, version)
                
                return True
            else:
                logger.error(f"Failed to install {install_spec}: {result.stderr}")
                return False
                
        except subprocess.CalledProcessError as e:
            logger.error(f"Package installation failed: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during package installation: {e}")
            return False
    
    def install_dependencies(self, required_packages: List[str]) -> Dict[str, bool]:
        """Install multiple dependencies.
        
        Args:
            required_packages: List of package names to install
            
        Returns:
            Dictionary mapping package names to installation success
        """
        results = {}
        
        # 检查哪些包需要安装
        status = self.check_dependencies(required_packages)
        
        for package in required_packages:
            if status[package]:
                logger.info(f"Package {package} is already installed")
                results[package] = True
            else:
                results[package] = self.install_package(package)
        
        return results
    
    def _update_requirements_file(self, package_name: str, version: Optional[str] = None) -> None:
        """Update the requirements.txt file with a new package.
        
        Args:
            package_name: Package name
            version: Optional version specification
        """
        try:
            # 读取现有requirements
            existing_requirements = set()
            if self.requirements_file.exists():
                with open(self.requirements_file, 'r', encoding='utf-8') as f:
                    existing_requirements = set(line.strip() for line in f if line.strip() and not line.startswith('#'))
            
            # 构建新的requirement行
            if version:
                new_requirement = f"{package_name}>={version}"
            else:
                # 获取当前安装的版本
                package_info = self.installed_packages.get(package_name.lower())
                if package_info and package_info.installed_version:
                    new_requirement = f"{package_name}>={package_info.installed_version}"
                else:
                    new_requirement = package_name
            
            # 检查是否已存在类似的requirement
            package_exists = any(req.split('>=')[0].split('==')[0].split('~=')[0] == package_name 
                               for req in existing_requirements)
            
            if not package_exists:
                existing_requirements.add(new_requirement)
                
                # 写回文件
                with open(self.requirements_file, 'w', encoding='utf-8') as f:
                    for req in sorted(existing_requirements):
                        f.write(f"{req}\n")
                
                logger.info(f"Updated requirements.txt with: {new_requirement}")
        
        except Exception as e:
            logger.error(f"Failed to update requirements.txt: {e}")
    
    def suggest_alternatives(self, package_name: str) -> List[str]:
        """Suggest alternative packages if the requested one is not available.
        
        Args:
            package_name: Package name that couldn't be found
            
        Returns:
            List of suggested alternative package names
        """
        # 这里可以实现包名相似性搜索或者使用PyPI搜索API
        # 简化实现：基于常见的包名映射
        alternatives_map = {
            'cv2': ['opencv-python', 'opencv-contrib-python'],
            'PIL': ['Pillow'],
            'sklearn': ['scikit-learn'],
            'yaml': ['PyYAML'],
            'crypto': ['pycryptodome', 'cryptography'],
            'dateutil': ['python-dateutil'],
            'jwt': ['PyJWT'],
            'bs4': ['beautifulsoup4'],
            'requests_oauthlib': ['requests-oauthlib']
        }
        
        return alternatives_map.get(package_name, [])
    
    def auto_resolve_dependencies(self, code: str) -> Tuple[bool, List[str]]:
        """Automatically analyze and install dependencies for code.
        
        Args:
            code: Python code to analyze
            
        Returns:
            Tuple of (success, list of installed packages)
        """
        # 分析依赖
        required_packages = self.analyze_code_dependencies(code)
        
        if not required_packages:
            logger.info("No external dependencies found")
            return True, []
        
        # 安装依赖
        installation_results = self.install_dependencies(required_packages)
        
        # 检查结果
        failed_packages = [pkg for pkg, success in installation_results.items() if not success]
        successful_packages = [pkg for pkg, success in installation_results.items() if success]
        
        if failed_packages:
            logger.warning(f"Failed to install packages: {failed_packages}")
            
            # 尝试建议替代方案
            for failed_pkg in failed_packages:
                alternatives = self.suggest_alternatives(failed_pkg)
                if alternatives:
                    logger.info(f"Suggested alternatives for {failed_pkg}: {alternatives}")
                    
                    # 尝试安装第一个替代方案
                    for alt in alternatives:
                        if self.install_package(alt):
                            successful_packages.append(alt)
                            break
        
        success = len(failed_packages) == 0
        return success, successful_packages
    
    def get_dependency_tree(self, package_name: str) -> Dict[str, List[str]]:
        """Get the dependency tree for a package.
        
        Args:
            package_name: Package name
            
        Returns:
            Dictionary representing the dependency tree
        """
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "show", package_name],
                capture_output=True,
                text=True,
                check=True
            )
            
            dependencies = []
            for line in result.stdout.split('\n'):
                if line.startswith('Requires:'):
                    deps_str = line.replace('Requires:', '').strip()
                    if deps_str:
                        dependencies = [dep.strip() for dep in deps_str.split(',')]
                    break
            
            return {package_name: dependencies}
            
        except Exception as e:
            logger.error(f"Failed to get dependency tree for {package_name}: {e}")
            return {package_name: []}
    
    def cleanup_unused_packages(self) -> List[str]:
        """Identify and optionally remove unused packages.
        
        Returns:
            List of potentially unused packages
        """
        # 这是一个简化的实现
        # 实际应用中需要更复杂的依赖分析
        
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "list", "--not-required", "--format=json"],
                capture_output=True,
                text=True,
                check=True
            )
            
            unused_packages = json.loads(result.stdout)
            package_names = [pkg["name"] for pkg in unused_packages]
            
            logger.info(f"Found {len(package_names)} potentially unused packages")
            return package_names
            
        except Exception as e:
            logger.error(f"Failed to identify unused packages: {e}")
            return []
