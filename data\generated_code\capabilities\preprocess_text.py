import logging
import string
from typing import List, Optional

import nltk
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer, WordNetLemmatizer
from nltk.tokenize import word_tokenize

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Download necessary NLTK data
try:
    nltk.download("punkt", quiet=True)
    nltk.download("stopwords", quiet=True)
    nltk.download("wordnet", quiet=True)
except Exception as e:
    logger.error(f"Error downloading NLTK data: {e}")
    raise


def preprocess_text(
    text: str,
    lowercase: bool = True,
    remove_punctuation: bool = True,
    remove_stopwords: bool = True,
    stemming: bool = False,
    lemmatization: bool = False,
    custom_stopwords: Optional[List[str]] = None,
) -> str:
    """
    Preprocesses the input text by applying specified transformations such as lowercasing,
    removing punctuation, stopwords, and applying stemming or lemmatization.

    Args:
        text: The input text to be preprocessed.
        lowercase: Whether to convert the text to lowercase. Default is True.
        remove_punctuation: Whether to remove punctuation from the text. Default is True.
        remove_stopwords: Whether to remove stopwords from the text. Default is True.
        stemming: Whether to apply stemming to the text. Default is False.
        lemmatization: Whether to apply lemmatization to the text. Default is False.
        custom_stopwords: A list of custom stopwords to remove in addition to the default stopwords.
            Default is None (treated as an empty list).

    Returns:
        str: The preprocessed text.

    Raises:
        ValueError: If both stemming and lemmatization are set to True.
    """
    if custom_stopwords is None:
        custom_stopwords = []

    if stemming and lemmatization:
        raise ValueError(
            "Both stemming and lemmatization cannot be True simultaneously."
        )

    try:
        logger.info("Starting text preprocessing...")

        # Lowercase
        if lowercase:
            logger.debug("Converting text to lowercase.")
            text = text.lower()

        # Remove punctuation
        if remove_punctuation:
            logger.debug("Removing punctuation.")
            text = text.translate(str.maketrans("", "", string.punctuation))

        # Tokenize
        tokens = word_tokenize(text)
        logger.debug(f"Tokenized text: {tokens}")

        # Remove stopwords
        if remove_stopwords:
            logger.debug("Removing stopwords.")
            stop_words = set(stopwords.words("english")).union(set(custom_stopwords))
            tokens = [token for token in tokens if token not in stop_words]

        # Stemming
        if stemming:
            logger.debug("Applying stemming.")
            stemmer = PorterStemmer()
            tokens = [stemmer.stem(token) for token in tokens]

        # Lemmatization
        if lemmatization:
            logger.debug("Applying lemmatization.")
            lemmatizer = WordNetLemmatizer()
            tokens = [lemmatizer.lemmatize(token) for token in tokens]

        # Join tokens back to string
        preprocessed_text = " ".join(tokens)
        logger.info("Text preprocessing completed successfully.")
        return preprocessed_text

    except Exception as e:
        logger.error(f"Error during text preprocessing: {e}")
        raise
